/**
 * BDPay Admin Transactions Scripts
 *
 * @package BDPay
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Handle payment verification
    $('.bdpay-verify-btn').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(bdpay_admin_params.i18n.verify_confirm)) {
            return;
        }

        var $btn = $(this);
        var orderId = $btn.data('order-id');
        var originalText = $btn.text();

        $btn.text(bdpay_admin_params.i18n.processing).prop('disabled', true);

        $.ajax({
            url: bdpay_admin_params.ajax_url,
            type: 'POST',
            data: {
                action: 'bdpay_verify_payment',
                order_id: orderId,
                nonce: bdpay_admin_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || bdpay_admin_params.i18n.error_occurred);
                    $btn.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                alert(bdpay_admin_params.i18n.error_occurred);
                $btn.text(originalText).prop('disabled', false);
            }
        });
    });

    // Handle payment rejection
    $('.bdpay-reject-btn').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(bdpay_admin_params.i18n.reject_confirm)) {
            return;
        }

        var $btn = $(this);
        var orderId = $btn.data('order-id');
        var originalText = $btn.text();

        $btn.text(bdpay_admin_params.i18n.processing).prop('disabled', true);

        $.ajax({
            url: bdpay_admin_params.ajax_url,
            type: 'POST',
            data: {
                action: 'bdpay_reject_payment',
                order_id: orderId,
                nonce: bdpay_admin_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || bdpay_admin_params.i18n.error_occurred);
                    $btn.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                alert(bdpay_admin_params.i18n.error_occurred);
                $btn.text(originalText).prop('disabled', false);
            }
        });
    });

    // Copy functionality
    $('.bdpay-copy-btn').on('click', function(e) {
        e.preventDefault();
        var textToCopy = $(this).data('copy');

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(textToCopy).then(function() {
                alert(bdpay_admin_params.i18n.copied);
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert(bdpay_admin_params.i18n.copied);
        }
    });

    // Add loading animation styles
    if (!$('#bdpay-admin-styles').length) {
        $('<style id="bdpay-admin-styles">.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>').appendTo('head');
    }
});
