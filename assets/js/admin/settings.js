/**
 * BDPay Admin Settings Scripts
 *
 * @package BDPay
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Handle payment method enable/disable
    $('.bdpay-method-enable').on('change', function() {
        var $checkbox = $(this);
        var $methodSection = $checkbox.closest('.bdpay-method-section');
        var $fields = $methodSection.find('.bdpay-method-fields');

        if ($checkbox.is(':checked')) {
            $fields.slideDown();
            $methodSection.addClass('enabled');
        } else {
            $fields.slideUp();
            $methodSection.removeClass('enabled');
        }
    });

    // Initialize method sections on page load
    $('.bdpay-method-enable').each(function() {
        var $checkbox = $(this);
        var $methodSection = $checkbox.closest('.bdpay-method-section');
        var $fields = $methodSection.find('.bdpay-method-fields');

        if ($checkbox.is(':checked')) {
            $fields.show();
            $methodSection.addClass('enabled');
        } else {
            $fields.hide();
            $methodSection.removeClass('enabled');
        }
    });

    // Form validation
    $('form').on('submit', function() {
        var hasEnabledMethod = false;
        
        $('.bdpay-method-enable:checked').each(function() {
            var $checkbox = $(this);
            var method = $checkbox.val();
            var walletNumber = $('input[name="bdpay_wallet_' + method + '"]').val().trim();
            
            if (walletNumber) {
                hasEnabledMethod = true;
                return false; // Break the loop
            }
        });

        if (!hasEnabledMethod) {
            alert('Please enable at least one payment method with a valid wallet number.');
            return false;
        }

        return true;
    });
});
