/**
 * BDPay Admin Order Scripts
 *
 * @package BDPay
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Handle payment verification from order page
    $('.bdpay-verify-payment').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(bdpay_order_params.i18n.verify_confirm)) {
            return;
        }

        var $btn = $(this);
        var orderId = $btn.data('order-id');
        var originalText = $btn.text();

        $btn.text(bdpay_order_params.i18n.processing).prop('disabled', true);

        $.ajax({
            url: bdpay_order_params.ajax_url,
            type: 'POST',
            data: {
                action: 'bdpay_verify_payment',
                order_id: orderId,
                nonce: bdpay_order_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || bdpay_order_params.i18n.error_occurred);
                    $btn.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                alert(bdpay_order_params.i18n.error_occurred);
                $btn.text(originalText).prop('disabled', false);
            }
        });
    });

    // Handle payment rejection from order page
    $('.bdpay-reject-payment').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(bdpay_order_params.i18n.reject_confirm)) {
            return;
        }

        var $btn = $(this);
        var orderId = $btn.data('order-id');
        var originalText = $btn.text();

        $btn.text(bdpay_order_params.i18n.processing).prop('disabled', true);

        $.ajax({
            url: bdpay_order_params.ajax_url,
            type: 'POST',
            data: {
                action: 'bdpay_reject_payment',
                order_id: orderId,
                nonce: bdpay_order_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || bdpay_order_params.i18n.error_occurred);
                    $btn.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                alert(bdpay_order_params.i18n.error_occurred);
                $btn.text(originalText).prop('disabled', false);
            }
        });
    });

    // Copy functionality for transaction details
    $('.bdpay-copy-btn').on('click', function(e) {
        e.preventDefault();
        var textToCopy = $(this).data('copy');

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(textToCopy).then(function() {
                alert(bdpay_order_params.i18n.copied);
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert(bdpay_order_params.i18n.copied);
        }
    });
});
