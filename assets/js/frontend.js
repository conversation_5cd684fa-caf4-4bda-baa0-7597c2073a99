/**
 * BDPay Frontend JavaScript
 *
 * @package BDPay
 * @since 1.0.0
 */

(function($) {
    'use strict';

    var BDPay = {
        
        /**
         * Initialize BDPay frontend functionality
         */
        init: function() {
            this.bindEvents();
            this.initPaymentMethodSelection();
            this.initPaymentForm();
            this.initCopyButtons();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            $(document).on('change', 'input[name="bdpay_payment_method"]', this.handlePaymentMethodChange);
            $(document).on('change', '#bdpay_payment_method', this.handlePaymentMethodDropdownChange);
            $(document).on('submit', '#bdpay-payment-submission', this.handlePaymentSubmission);
            $(document).on('click', '.bdpay-copy-btn', this.handleCopyClick);
            $(document).on('input', '#bdpay_sender_phone', this.validatePhoneNumber);
            $(document).on('input', '#bdpay_transaction_id', this.validateTransactionId);
        },

        /**
         * Initialize payment method selection on checkout
         */
        initPaymentMethodSelection: function() {
            // Auto-select first payment method if only one is available
            var $methods = $('input[name="bdpay_payment_method"]');
            if ($methods.length === 1) {
                $methods.first().prop('checked', true).trigger('change');
            }
        },

        /**
         * Initialize payment form functionality
         */
        initPaymentForm: function() {
            // Auto-focus first input field
            $('#bdpay_payment_method').focus();
            
            // Initialize form validation
            this.initFormValidation();
        },

        /**
         * Initialize copy buttons
         */
        initCopyButtons: function() {
            // Check if clipboard API is available
            if (!navigator.clipboard) {
                $('.bdpay-copy-btn').hide();
            }
        },

        /**
         * Handle payment method change (radio buttons)
         */
        handlePaymentMethodChange: function() {
            var selectedMethod = $(this).val();
            
            // Hide all method details
            $('.bdpay-method-details').hide();
            
            // Show selected method details
            if (selectedMethod) {
                $('#bdpay-details-' + selectedMethod).show();
            }
        },

        /**
         * Handle payment method dropdown change
         */
        handlePaymentMethodDropdownChange: function() {
            var selectedMethod = $(this).val();
            
            // Hide all method info
            $('.bdpay-method-info').hide();
            $('#bdpay-method-info').hide();
            
            // Show selected method info
            if (selectedMethod) {
                $('#bdpay-method-info').show();
                $('#bdpay-info-' + selectedMethod).show();
            }
        },

        /**
         * Handle payment form submission
         */
        handlePaymentSubmission: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('.bdpay-submit-btn');
            
            // Validate form
            if (!BDPay.validateForm($form)) {
                return false;
            }
            
            // Disable submit button and show loading
            $submitBtn.prop('disabled', true)
                     .html('<span class="bdpay-spinner"></span>' + bdpay_params.i18n.submitting);
            
            // Prepare form data
            var formData = new FormData(this);
            
            // Submit via AJAX
            $.ajax({
                url: bdpay_params.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        BDPay.showSuccess(response.data.message);
                        
                        // Redirect if URL provided
                        if (response.data.redirect) {
                            setTimeout(function() {
                                window.location.href = response.data.redirect;
                            }, 2000);
                        } else {
                            // Reload page to show updated status
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    } else {
                        BDPay.showError(response.data);
                        BDPay.resetSubmitButton($submitBtn);
                    }
                },
                error: function(xhr, status, error) {
                    BDPay.showError(bdpay_params.i18n.error_occurred || 'An error occurred. Please try again.');
                    BDPay.resetSubmitButton($submitBtn);
                }
            });
        },

        /**
         * Handle copy button clicks
         */
        handleCopyClick: function(e) {
            e.preventDefault();
            
            var textToCopy = $(this).data('copy');
            var $btn = $(this);
            
            if (navigator.clipboard && textToCopy) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    BDPay.showCopySuccess($btn);
                }).catch(function() {
                    BDPay.showCopyError($btn);
                });
            } else {
                BDPay.showCopyError($btn);
            }
        },

        /**
         * Validate phone number input
         */
        validatePhoneNumber: function() {
            var phone = $(this).val();
            var $field = $(this);
            
            // Remove any existing validation classes
            $field.removeClass('bdpay-valid bdpay-invalid');
            
            if (phone.length > 0) {
                // Basic validation for Bangladesh phone numbers
                var cleanPhone = phone.replace(/[^0-9]/g, '');
                var isValid = /^(01[3-9]\d{8}|8801[3-9]\d{8})$/.test(cleanPhone);
                
                if (isValid) {
                    $field.addClass('bdpay-valid');
                } else if (cleanPhone.length >= 11) {
                    $field.addClass('bdpay-invalid');
                }
            }
        },

        /**
         * Validate transaction ID input
         */
        validateTransactionId: function() {
            var transactionId = $(this).val();
            var $field = $(this);
            
            // Remove any existing validation classes
            $field.removeClass('bdpay-valid bdpay-invalid');
            
            if (transactionId.length > 0) {
                // Basic validation - should be alphanumeric and at least 8 characters
                var isValid = /^[A-Za-z0-9]{8,}$/.test(transactionId);
                
                if (isValid) {
                    $field.addClass('bdpay-valid');
                } else if (transactionId.length >= 8) {
                    $field.addClass('bdpay-invalid');
                }
            }
        },

        /**
         * Validate entire form
         */
        validateForm: function($form) {
            var isValid = true;
            var errors = [];
            
            // Check payment method
            var paymentMethod = $form.find('#bdpay_payment_method').val();
            if (!paymentMethod) {
                errors.push(bdpay_params.i18n.payment_method_required);
                isValid = false;
            }
            
            // Check transaction ID
            var transactionId = $form.find('#bdpay_transaction_id').val();
            if (!transactionId || transactionId.length < 8) {
                errors.push(bdpay_params.i18n.transaction_id_required);
                isValid = false;
            }
            
            // Check phone number
            var phone = $form.find('#bdpay_sender_phone').val();
            if (!phone) {
                errors.push(bdpay_params.i18n.phone_required);
                isValid = false;
            } else {
                var cleanPhone = phone.replace(/[^0-9]/g, '');
                if (!/^(01[3-9]\d{8}|8801[3-9]\d{8})$/.test(cleanPhone)) {
                    errors.push(bdpay_params.i18n.invalid_phone);
                    isValid = false;
                }
            }
            
            // Show errors if any
            if (!isValid) {
                this.showError(errors.join('<br>'));
            }
            
            return isValid;
        },

        /**
         * Initialize form validation styling
         */
        initFormValidation: function() {
            // Add validation styles to CSS if not already present
            if (!$('#bdpay-validation-styles').length) {
                $('<style id="bdpay-validation-styles">')
                    .text(`
                        .bdpay-valid {
                            border-color: #28a745 !important;
                            box-shadow: 0 0 5px rgba(40, 167, 69, 0.3) !important;
                        }
                        .bdpay-invalid {
                            border-color: #dc3545 !important;
                            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3) !important;
                        }
                    `)
                    .appendTo('head');
            }
        },

        /**
         * Reset submit button to original state
         */
        resetSubmitButton: function($btn) {
            $btn.prop('disabled', false)
                .text(bdpay_params.i18n.submit_payment);
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.showNotice(message, 'error');
        },

        /**
         * Show copy success feedback
         */
        showCopySuccess: function($btn) {
            var originalText = $btn.text();
            $btn.text(bdpay_params.i18n.copied)
                .addClass('bdpay-copy-success');
            
            setTimeout(function() {
                $btn.text(originalText)
                    .removeClass('bdpay-copy-success');
            }, 2000);
        },

        /**
         * Show copy error feedback
         */
        showCopyError: function($btn) {
            var originalText = $btn.text();
            $btn.text(bdpay_params.i18n.copy_failed)
                .addClass('bdpay-copy-error');
            
            setTimeout(function() {
                $btn.text(originalText)
                    .removeClass('bdpay-copy-error');
            }, 3000);
        },

        /**
         * Show notification message
         */
        showNotice: function(message, type) {
            // Remove existing notices
            $('.bdpay-notice').remove();
            
            // Create notice element
            var $notice = $('<div class="bdpay-notice bdpay-notice-' + type + '">')
                .html(message)
                .hide();
            
            // Insert notice at the top of the form or page
            var $target = $('.bdpay-payment-form, .bdpay-payment-fields').first();
            if ($target.length) {
                $target.prepend($notice);
            } else {
                $('body').prepend($notice);
            }
            
            // Show notice with animation
            $notice.slideDown();
            
            // Auto-hide success notices
            if (type === 'success') {
                setTimeout(function() {
                    $notice.slideUp(function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        },

        /**
         * Format phone number for display
         */
        formatPhoneNumber: function(phone) {
            var cleanPhone = phone.replace(/[^0-9]/g, '');
            
            if (/^01[3-9]\d{8}$/.test(cleanPhone)) {
                return cleanPhone.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1-$2-$3');
            } else if (/^8801[3-9]\d{8}$/.test(cleanPhone)) {
                return cleanPhone.replace(/^(88)(\d{3})(\d{4})(\d{4})$/, '+$1-$2-$3-$4');
            }
            
            return phone;
        },

        /**
         * Get wallet info via AJAX
         */
        getWalletInfo: function(method, callback) {
            $.ajax({
                url: bdpay_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'bdpay_get_wallet_info',
                    method: method,
                    nonce: bdpay_params.nonce
                },
                success: function(response) {
                    if (response.success && callback) {
                        callback(response.data);
                    }
                },
                error: function() {
                    console.log('Failed to get wallet info');
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        BDPay.init();
    });

    // Make BDPay object globally available
    window.BDPay = BDPay;

})(jQuery);
