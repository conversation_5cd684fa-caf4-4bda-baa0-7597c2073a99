/**
 * BDPay Admin JavaScript
 *
 * @package BDPay
 * @since 1.0.0
 */

(function($) {
    'use strict';

    var BDPayAdmin = {
        
        /**
         * Initialize BDPay admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initSettingsPage();
            this.initOrderMetaBox();
            this.initTransactionsPage();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            $(document).on('click', '.bdpay-copy-btn', this.handleCopyClick);
            $(document).on('click', '.bdpay-verify-btn, .bdpay-reject-btn', this.handleVerificationAction);
            $(document).on('click', '.nav-tab', this.handleTabSwitch);
            $(document).on('change', 'input[name^="bdpay_enable_"]', this.handleMethodToggle);
        },

        /**
         * Initialize settings page functionality
         */
        initSettingsPage: function() {
            // Initialize tab switching
            this.initTabs();
            
            // Initialize method toggles
            this.initMethodToggles();
            
            // Initialize form validation
            this.initSettingsValidation();
        },

        /**
         * Initialize order meta box functionality
         */
        initOrderMetaBox: function() {
            // Auto-refresh order status if needed
            this.checkOrderStatus();
        },

        /**
         * Initialize transactions page functionality
         */
        initTransactionsPage: function() {
            // Initialize transaction filters
            this.initTransactionFilters();
            
            // Initialize bulk actions
            this.initBulkActions();
        },

        /**
         * Handle copy button clicks
         */
        handleCopyClick: function(e) {
            e.preventDefault();
            
            var textToCopy = $(this).data('copy');
            var $btn = $(this);
            
            if (navigator.clipboard && textToCopy) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    BDPayAdmin.showCopySuccess($btn);
                }).catch(function() {
                    BDPayAdmin.showCopyError($btn);
                });
            } else {
                // Fallback for older browsers
                BDPayAdmin.fallbackCopy(textToCopy, $btn);
            }
        },

        /**
         * Handle payment verification actions
         */
        handleVerificationAction: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var orderId = $btn.data('order-id');
            var action = $btn.data('action');
            var confirmMessage = action === 'verify' ? 
                bdpay_admin_params.i18n.confirm_verify : 
                bdpay_admin_params.i18n.confirm_reject;

            // Confirm action
            if (!confirm(confirmMessage)) {
                return;
            }

            // Disable button and show loading
            $btn.prop('disabled', true)
                .html('<span class="bdpay-spinner"></span>' + $btn.text());

            // Send AJAX request
            $.ajax({
                url: bdpay_admin_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'bdpay_verify_payment',
                    order_id: orderId,
                    action_type: action,
                    nonce: bdpay_admin_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        BDPayAdmin.showSuccess(response.data);
                        
                        // Reload page after short delay
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        BDPayAdmin.showError(response.data);
                        BDPayAdmin.resetButton($btn);
                    }
                },
                error: function() {
                    BDPayAdmin.showError('An error occurred. Please try again.');
                    BDPayAdmin.resetButton($btn);
                }
            });
        },

        /**
         * Handle tab switching
         */
        handleTabSwitch: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.attr('href');
            
            // Update tab appearance
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Show/hide content
            $('.tab-pane').removeClass('active');
            $(target).addClass('active');
            
            // Update URL hash
            if (history.pushState) {
                history.pushState(null, null, target);
            }
        },

        /**
         * Handle payment method enable/disable toggle
         */
        handleMethodToggle: function() {
            var $checkbox = $(this);
            var $settings = $checkbox.closest('.bdpay-method-config').find('.bdpay-method-settings');
            
            if ($checkbox.is(':checked')) {
                $settings.slideDown();
            } else {
                $settings.slideUp();
            }
        },

        /**
         * Initialize tabs functionality
         */
        initTabs: function() {
            // Show tab based on URL hash
            var hash = window.location.hash;
            if (hash && $(hash).length) {
                $('.nav-tab[href="' + hash + '"]').trigger('click');
            }
        },

        /**
         * Initialize method toggles
         */
        initMethodToggles: function() {
            $('input[name^="bdpay_enable_"]').each(function() {
                var $checkbox = $(this);
                var $settings = $checkbox.closest('.bdpay-method-config').find('.bdpay-method-settings');
                
                if (!$checkbox.is(':checked')) {
                    $settings.hide();
                }
            });
        },

        /**
         * Initialize settings form validation
         */
        initSettingsValidation: function() {
            $('form').on('submit', function(e) {
                var isValid = true;
                var errors = [];
                
                // Validate enabled methods have wallet numbers
                $('input[name^="bdpay_enable_"]:checked').each(function() {
                    var method = $(this).attr('name').replace('bdpay_enable_', '');
                    var walletInput = $('input[name="bdpay_wallet_' + method + '"]');
                    
                    if (!walletInput.val().trim()) {
                        errors.push('Please enter a wallet number for ' + method);
                        isValid = false;
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    BDPayAdmin.showError(errors.join('<br>'));
                }
            });
        },

        /**
         * Initialize transaction filters
         */
        initTransactionFilters: function() {
            // Add filter functionality if on transactions page
            if ($('.bdpay-transactions-table').length) {
                this.setupTransactionFilters();
            }
        },

        /**
         * Setup transaction filters
         */
        setupTransactionFilters: function() {
            // Status filter
            $('<select id="bdpay-status-filter">')
                .append('<option value="">All Statuses</option>')
                .append('<option value="pending">Pending</option>')
                .append('<option value="submitted">Submitted</option>')
                .append('<option value="verified">Verified</option>')
                .append('<option value="rejected">Rejected</option>')
                .on('change', this.filterTransactions)
                .insertBefore('.bdpay-transactions-table');
        },

        /**
         * Filter transactions based on selected criteria
         */
        filterTransactions: function() {
            var status = $('#bdpay-status-filter').val();
            var $rows = $('.bdpay-transactions-table tbody tr');
            
            $rows.show();
            
            if (status) {
                $rows.each(function() {
                    var rowStatus = $(this).find('.bdpay-status').data('status');
                    if (rowStatus !== status) {
                        $(this).hide();
                    }
                });
            }
        },

        /**
         * Initialize bulk actions
         */
        initBulkActions: function() {
            // Add bulk action functionality if needed
            if ($('.bdpay-transactions-table').length) {
                this.setupBulkActions();
            }
        },

        /**
         * Setup bulk actions for transactions
         */
        setupBulkActions: function() {
            // Add checkboxes to transaction rows
            $('.bdpay-transactions-table tbody tr').each(function() {
                $(this).prepend('<td><input type="checkbox" class="bdpay-transaction-checkbox"></td>');
            });
            
            // Add header checkbox
            $('.bdpay-transactions-table thead tr').prepend('<th><input type="checkbox" id="bdpay-select-all"></th>');
            
            // Handle select all
            $('#bdpay-select-all').on('change', function() {
                $('.bdpay-transaction-checkbox').prop('checked', $(this).is(':checked'));
            });
        },

        /**
         * Check order status and update if needed
         */
        checkOrderStatus: function() {
            // Auto-refresh functionality could be added here
            // For now, just ensure the meta box is properly displayed
        },

        /**
         * Reset button to original state
         */
        resetButton: function($btn) {
            var originalText = $btn.data('original-text') || $btn.text().replace(/^.*?(\w+)$/, '$1');
            $btn.prop('disabled', false).html(originalText);
        },

        /**
         * Show copy success feedback
         */
        showCopySuccess: function($btn) {
            var originalText = $btn.text();
            $btn.text('Copied!')
                .addClass('bdpay-copy-success');
            
            setTimeout(function() {
                $btn.text(originalText)
                    .removeClass('bdpay-copy-success');
            }, 2000);
        },

        /**
         * Show copy error feedback
         */
        showCopyError: function($btn) {
            var originalText = $btn.text();
            $btn.text('Failed')
                .addClass('bdpay-copy-error');
            
            setTimeout(function() {
                $btn.text(originalText)
                    .removeClass('bdpay-copy-error');
            }, 3000);
        },

        /**
         * Fallback copy method for older browsers
         */
        fallbackCopy: function(text, $btn) {
            var $temp = $('<textarea>').val(text).appendTo('body').select();
            
            try {
                document.execCommand('copy');
                this.showCopySuccess($btn);
            } catch (err) {
                this.showCopyError($btn);
            }
            
            $temp.remove();
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.showNotice(message, 'error');
        },

        /**
         * Show admin notice
         */
        showNotice: function(message, type) {
            // Remove existing notices
            $('.bdpay-admin-notice').remove();
            
            // Create notice
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible bdpay-admin-notice">')
                .append('<p>' + message + '</p>')
                .append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');
            
            // Insert notice
            if ($('.wrap h1').length) {
                $('.wrap h1').after($notice);
            } else {
                $('.wrap').prepend($notice);
            }
            
            // Handle dismiss
            $notice.find('.notice-dismiss').on('click', function() {
                $notice.fadeOut();
            });
            
            // Auto-hide success notices
            if (type === 'success') {
                setTimeout(function() {
                    $notice.fadeOut();
                }, 5000);
            }
        },

        /**
         * Validate wallet number format
         */
        validateWalletNumber: function(number) {
            // Remove any non-digit characters
            var cleanNumber = number.replace(/[^0-9]/g, '');
            
            // Check if it's a valid Bangladesh mobile number
            return /^(01[3-9]\d{8}|8801[3-9]\d{8})$/.test(cleanNumber);
        },

        /**
         * Format wallet number for display
         */
        formatWalletNumber: function(number) {
            var cleanNumber = number.replace(/[^0-9]/g, '');
            
            if (/^01[3-9]\d{8}$/.test(cleanNumber)) {
                return cleanNumber.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1-$2-$3');
            } else if (/^8801[3-9]\d{8}$/.test(cleanNumber)) {
                return cleanNumber.replace(/^(88)(\d{3})(\d{4})(\d{4})$/, '+$1-$2-$3-$4');
            }
            
            return number;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        BDPayAdmin.init();
    });

    // Make BDPayAdmin object globally available
    window.BDPayAdmin = BDPayAdmin;

})(jQuery);
