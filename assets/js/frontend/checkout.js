/**
 * BDPay Frontend Checkout Scripts
 *
 * @package BDPay
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Handle payment method selection
    $('input[name="bdpay_payment_method"]').on('change', function() {
        var selectedMethod = $(this).val();

        // Update selection states
        $('.bdpay-method-option').removeClass('selected');
        $(this).closest('.bdpay-method-option').addClass('selected');

        // Hide all method details
        $('.bdpay-method-details').removeClass('show');
        $('#bdpay-payment-submission').hide();

        if (selectedMethod) {
            // Show selected method details and submission form
            $('#bdpay-details-' + selectedMethod).addClass('show');
            $('#bdpay-payment-submission').show();
        }
    });

    // Auto-select and show details if only one payment method is available
    function initializePaymentMethods() {
        var $paymentMethods = $('input[name="bdpay_payment_method"]');
        
        if ($paymentMethods.length === 1) {
            // Only one payment method - auto-select it
            var $singleMethod = $paymentMethods.first();
            $singleMethod.prop('checked', true);
            $singleMethod.trigger('change');
        } else if ($paymentMethods.length > 1) {
            // Multiple methods - check if one is already selected
            var $checkedMethod = $paymentMethods.filter(':checked');
            if ($checkedMethod.length > 0) {
                $checkedMethod.trigger('change');
            }
        }
    }

    // Initialize on page load
    initializePaymentMethods();

    // Copy functionality
    $('.bdpay-copy-btn').on('click', function(e) {
        e.preventDefault();
        var $btn = $(this);
        var textToCopy = $btn.data('copy');
        var originalIcon = $btn.html();

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(textToCopy).then(function() {
                showCopySuccess($btn, originalIcon);
            }).catch(function() {
                fallbackCopy(textToCopy, $btn, originalIcon);
            });
        } else {
            fallbackCopy(textToCopy, $btn, originalIcon);
        }
    });

    function showCopySuccess($btn, originalIcon) {
        $btn.html('✓').addClass('copied');
        setTimeout(function() {
            $btn.html(originalIcon).removeClass('copied');
        }, 2000);
    }

    function fallbackCopy(text, $btn, originalIcon) {
        var textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            showCopySuccess($btn, originalIcon);
        } catch (err) {
            console.error('Copy failed:', err);
        }
        
        document.body.removeChild(textArea);
    }

    // Form validation
    $('form.checkout').on('checkout_place_order_bdpay', function() {
        var paymentMethod = $('input[name="bdpay_payment_method"]:checked').val();
        var transactionId = $('#bdpay_transaction_id').val().trim();
        var senderPhone = $('#bdpay_sender_phone').val().trim();

        if (!paymentMethod) {
            alert(bdpay_params.i18n.select_method);
            return false;
        }

        if (!transactionId) {
            alert(bdpay_params.i18n.enter_transaction);
            return false;
        }

        if (!senderPhone) {
            alert(bdpay_params.i18n.enter_phone);
            return false;
        }

        if (!/^01[3-9]\d{8}$/.test(senderPhone.replace(/[\s\-\+]/g, ''))) {
            alert(bdpay_params.i18n.invalid_phone);
            return false;
        }

        return true;
    });
});
