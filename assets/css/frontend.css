/**
 * BDPay Frontend Styles
 *
 * @package BDPay
 * @since 1.0.0
 */

/* Simple Payment Fields Styles */
.bdpay-payment-fields {
    margin: 20px 0;
    font-family: inherit;
    line-height: 1.5;
}

.bdpay-empty-icon {
    margin-bottom: 46px !important;
}

/* Section Titles */
.bdpay-section-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.bdpay-section-description {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
}

/* Simple Payment Methods List */
.bdpay-methods-list {
    margin-bottom: 20px;
}

.bdpay-method-option {
    margin-bottom: 10px;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: border-color 0.2s ease;
}

.bdpay-method-option:hover {
    border-color: #0073aa;
}

.bdpay-method-option.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

.bdpay-method-option input[type="radio"] {
    margin-right: 10px;
}

.bdpay-method-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
    font-weight: normal;
    font-size: 14px;
}

p.bdpay-section-description {
    margin-bottom: 20px !important;
}

button.bdpay-copy-btn {
    width: 30px !important;
    position: absolute !important;
    top: 23px;
}

.bdpay-wallet-display {
    position: relative;
}

.bdpay-sender-phone-input input,
.bdpay-transaction-id-input input {
    border: 0 !important;
    box-shadow: none !important;
}

/* Simple Payment Details */
.bdpay-method-details {
    margin-top: 20px;
    padding: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    display: none !important;
    /* Hidden by default */
}

.bdpay-method-details.show {
    display: block !important;
    /* Show when selected */
}

.bdpay-payment-info h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.bdpay-wallet-info,
.bdpay-amount-info,
.bdpay-instructions {
    margin-bottom: 16px;
}

.bdpay-wallet-info:last-child,
.bdpay-amount-info:last-child,
.bdpay-instructions:last-child {
    margin-bottom: 0;
}

.bdpay-wallet-info label,
.bdpay-amount-info label,
.bdpay-instructions label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.bdpay-wallet-display {
    position: relative;
    padding: 10px 40px 10px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.bdpay-wallet-number {
    font-family: monospace;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    display: block;
    width: 100%;
}

.bdpay-amount-display {
    padding: 10px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.bdpay-instructions-text {
    padding: 10px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    color: #555;
    line-height: 1.5;
}

/* Modern Payment Info Cards */
.bdpay-payment-info-card {
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.bdpay-card-header {
    padding: 24px 24px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.bdpay-card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.3;
}

.bdpay-method-badge {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.bdpay-method-badge img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.bdpay-card-body {
    padding: 24px;
}

/* Step-by-step sections */
.bdpay-wallet-section,
.bdpay-amount-section,
.bdpay-instructions-section {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    align-items: flex-start;
}

.bdpay-wallet-section:last-child,
.bdpay-amount-section:last-child,
.bdpay-instructions-section:last-child {
    margin-bottom: 0;
}

.bdpay-step-number {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.bdpay-step-content {
    flex: 1;
}

.bdpay-step-content h5 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.bdpay-wallet-display {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-top: 8px;
}

.bdpay-wallet-number {
    flex: 1;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    letter-spacing: 0.5px;
}

.wallet-number {
    font-family: monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin: 0 10px;
}

.bdpay-copy-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    padding: 0;
    background: #f0f0f0;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.bdpay-copy-btn:hover {
    background: #e0e0e0;
    border-color: #0073aa;
    color: #0073aa;
}

.bdpay-copy-btn.copied {
    background: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

/* Simple Payment Submission */
.bdpay-payment-submission {
    margin-top: 20px;
    padding: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.bdpay-payment-submission h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.bdpay-payment-submission p {
    margin: 0 0 16px 0;
    color: #666;
    font-size: 14px;
}

.bdpay-form-fields {
    margin-top: 16px;
}

/* Payment Confirmation Fields - styled like Instructions */
.bdpay-transaction-id,
.bdpay-sender-phone,
.bdpay-notes {
    margin-bottom: 16px;
}

.bdpay-transaction-id:last-child,
.bdpay-sender-phone:last-child,
.bdpay-notes:last-child {
    margin-bottom: 0;
}

.bdpay-transaction-id label,
.bdpay-sender-phone label,
.bdpay-notes label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.bdpay-transaction-id .required,
.bdpay-sender-phone .required,
.bdpay-notes .required {
    color: #d63638;
}

.bdpay-transaction-id-input,
.bdpay-sender-phone-input,
.bdpay-notes-input {
    padding: 10px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.bdpay-transaction-id-input input,
.bdpay-sender-phone-input input,
.bdpay-notes-input textarea {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    font-family: inherit;
    resize: none;
}

.bdpay-transaction-id-input:focus-within,
.bdpay-sender-phone-input:focus-within,
.bdpay-notes-input:focus-within {
    border-color: #0073aa;
}

.bdpay-transaction-id small,
.bdpay-sender-phone small,
.bdpay-notes small {
    display: block;
    margin-top: 4px;
    color: #666;
    font-size: 12px;
}

/* Simple Notice */
.bdpay-notice {
    margin-top: 16px;
    padding: 12px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    font-size: 14px;
}

/* Simple Responsive Design */
@media (max-width: 768px) {
    .bdpay-payment-fields {
        margin: 16px 0;
    }

    .bdpay-method-option {
        padding: 10px 12px;
    }

    .bdpay-method-details,
    .bdpay-payment-submission {
        padding: 12px;
    }

    .bdpay-wallet-display {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .bdpay-copy-btn {
        align-self: flex-start;
    }

    .bdpay-form-row input,
    .bdpay-form-row textarea {
        font-size: 16px;
        /* Prevents zoom on iOS */
    }
}