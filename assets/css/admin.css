/**
 * BDPay Admin Styles
 *
 * @package BDPay
 * @since 1.0.0
 */

/* Order Meta Box Styles */
.bdpay-order-meta-box {
    padding: 10px 0;
}

.bdpay-meta-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.bdpay-meta-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
    line-height: 1.5;
}

.bdpay-meta-table td:first-child {
    width: 30%;
    background: #f9f9f9;
    font-weight: 600;
    color: #333;
}

.bdpay-meta-table td:last-child {
    background: #fff;
}

.bdpay-method-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #0073aa;
    color: white;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.bdpay-method-bkash {
    background: #e2136e;
}

.bdpay-method-nagad {
    background: #f47920;
}

.bdpay-method-rocket {
    background: #8b1538;
}

.bdpay-method-upay {
    background: #ff6b35;
}

.bdpay-method-surecash {
    background: #1e88e5;
}

.bdpay-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.bdpay-status-pending {
    background: #f0f0f1;
    color: #646970;
}

.bdpay-status-submitted {
    background: #fff3cd;
    color: #856404;
}

.bdpay-status-verified {
    background: #d4edda;
    color: #155724;
}

.bdpay-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.bdpay-copy-btn {
    margin-left: 8px;
    font-size: 11px;
    padding: 2px 6px;
    height: auto;
    line-height: 1.2;
    background: #f0f0f1;
    border: 1px solid #ccd0d4;
    color: #2c3338;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bdpay-copy-btn:hover {
    background: #e0e0e0;
    border-color: #999;
}

.bdpay-verification-actions {
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 15px;
}

.bdpay-verification-actions h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.bdpay-action-buttons {
    margin-bottom: 15px;
}

.bdpay-action-buttons .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.bdpay-verify-btn {
    background: #00a32a;
    border-color: #00a32a;
    color: white;
}

.bdpay-verify-btn:hover {
    background: #008a20;
    border-color: #008a20;
}

.bdpay-reject-btn {
    background: #d63638;
    border-color: #d63638;
    color: white;
}

.bdpay-reject-btn:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

.bdpay-action-buttons .dashicons {
    margin-right: 5px;
    vertical-align: middle;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.bdpay-verification-tips {
    background: #f0f6fc;
    border: 1px solid #c3dafe;
    border-radius: 3px;
    padding: 15px;
}

.bdpay-verification-tips h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0073aa;
}

.bdpay-verification-tips ul {
    margin: 0;
    padding-left: 20px;
}

.bdpay-verification-tips li {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.bdpay-verified-notice,
.bdpay-rejected-notice {
    padding: 12px 15px;
    border-radius: 3px;
    margin-top: 15px;
    font-weight: 500;
}

.bdpay-verified-notice {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.bdpay-rejected-notice {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.bdpay-verified-notice .dashicons,
.bdpay-rejected-notice .dashicons {
    margin-right: 5px;
    vertical-align: middle;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Settings Page Styles */
.bdpay-settings-page .nav-tab-wrapper {
    margin-bottom: 0;
}

.bdpay-settings-page .tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-top: none;
}

.bdpay-settings-page .tab-pane {
    display: none;
}

.bdpay-settings-page .tab-pane.active {
    display: block;
}

.bdpay-method-config {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
}

.bdpay-method-config h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.bdpay-method-config h3 input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.bdpay-method-settings {
    border-left: 3px solid #0073aa;
    padding-left: 15px;
    margin-left: 25px;
}

.bdpay-method-settings .form-table {
    margin-top: 0;
}

.bdpay-method-settings .form-table th {
    width: 200px;
    padding-left: 0;
}

.bdpay-method-settings .form-table td {
    padding-left: 0;
}

/* Transactions Page Styles */
.bdpay-transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.bdpay-transactions-table th,
.bdpay-transactions-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.bdpay-transactions-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #333;
}

.bdpay-transactions-table tr:hover {
    background: #f5f5f5;
}

.bdpay-transaction-id {
    font-family: monospace;
    font-size: 13px;
    background: #f0f0f1;
    padding: 2px 4px;
    border-radius: 2px;
}

.bdpay-phone-number {
    font-family: monospace;
    font-size: 13px;
}

.bdpay-amount {
    font-weight: 600;
    color: #0073aa;
}

.bdpay-date {
    font-size: 13px;
    color: #666;
}

/* BDPay Transactions Page - Refresh Button */
.bdpay-refresh-btn {
    background: #d61369 !important;
    border: 0 !important;
    padding: 10px 50px !important;
    display: flex !important;
    border-radius: 4px !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    align-items: center !important;
    gap: 8px !important;
    transition: background-color 0.2s ease !important;
}

.bdpay-refresh-btn:hover {
    background: #b8115a !important;
    color: #ffffff !important;
}

.bdpay-refresh-btn:focus {
    background: #b8115a !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 2px rgba(214, 19, 105, 0.3) !important;
}

/* Loading States */
.bdpay-loading {
    opacity: 0.6;
    pointer-events: none;
}

.bdpay-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: bdpay-spin 1s linear infinite;
    margin-right: 5px;
}

@keyframes bdpay-spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Notices */
.bdpay-notice {
    margin: 15px 0;
    padding: 10px 15px;
    border-radius: 3px;
}

.bdpay-notice-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.bdpay-notice-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.bdpay-notice-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.bdpay-notice-info {
    background: #cce5ff;
    border: 1px solid #99d6ff;
    color: #004085;
}

/* Responsive Admin Styles */
@media (max-width: 782px) {
    .bdpay-meta-table td {
        padding: 6px 8px;
        font-size: 14px;
    }

    .bdpay-meta-table td:first-child {
        width: 40%;
    }

    .bdpay-action-buttons .button {
        margin-bottom: 8px;
        font-size: 13px;
        padding: 6px 10px;
    }

    .bdpay-verification-tips {
        padding: 10px;
    }

    .bdpay-verification-tips li {
        font-size: 12px;
    }

    .bdpay-method-config {
        padding: 15px;
    }

    .bdpay-method-settings {
        margin-left: 15px;
        padding-left: 10px;
    }

    .bdpay-transactions-table {
        font-size: 13px;
    }

    .bdpay-transactions-table th,
    .bdpay-transactions-table td {
        padding: 6px 8px;
    }
}

/* Print Styles */
@media print {

    .bdpay-action-buttons,
    .bdpay-copy-btn {
        display: none;
    }

    .bdpay-meta-table {
        border: 1px solid #000;
    }

    .bdpay-meta-table td {
        border: 1px solid #000;
    }
}