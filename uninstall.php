<?php
/**
 * BDPay Uninstall
 *
 * Uninstalling BDPay deletes user data, tables, and options.
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'WP_UNINSTALL_PLUGIN' ) || exit;

// Security check
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
    exit;
}

/**
 * Only remove ALL plugin data if BDPAY_REMOVE_ALL_DATA constant is set to true in user's
 * wp-config.php. This is to prevent data loss when deleting the plugin from the backend
 * and to ensure only the site owner can perform this action.
 */
if ( defined( 'BDPAY_REMOVE_ALL_DATA' ) && true === BDPAY_REMOVE_ALL_DATA ) {
    
    global $wpdb;

    // Delete plugin options using prepared statements
    // phpcs:disable WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s", 'bdpay_%' ) );

    // Delete plugin transients
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s", '_transient_bdpay_%' ) );
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s", '_transient_timeout_bdpay_%' ) );

    // Delete plugin tables
    $table_name = $wpdb->prefix . 'bdpay_transactions';
    $wpdb->query( $wpdb->prepare( "DROP TABLE IF EXISTS %i", $table_name ) );

    // Delete plugin user meta
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE %s", 'bdpay_%' ) );

    // Delete plugin post meta
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE %s", '_bdpay_%' ) );

    // Delete plugin posts (if any custom post types were created)
    $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->posts} WHERE post_type LIKE %s", 'bdpay_%' ) );
    // phpcs:enable WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching

    // Clear any cached data that has been removed
    wp_cache_flush();
}
