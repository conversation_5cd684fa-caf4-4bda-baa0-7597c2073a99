<?php
/**
 * BDPay payment submission form template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="bdpay-payment-form">
    <h3><?php _e( 'Submit Your Payment Details', 'bdpay' ); ?></h3>
    
    <div class="bdpay-order-info">
        <p><strong><?php _e( 'Order Total:', 'bdpay' ); ?></strong> <?php echo wc_price( $order->get_total() ); ?></p>
        <p><strong><?php _e( 'Order Number:', 'bdpay' ); ?></strong> #<?php echo $order->get_order_number(); ?></p>
    </div>

    <form id="bdpay-payment-submission" method="post" enctype="multipart/form-data">
        <div class="bdpay-form-row">
            <label for="bdpay_payment_method"><?php _e( 'Payment Method', 'bdpay' ); ?> <span class="required">*</span></label>
            <select name="payment_method" id="bdpay_payment_method" required>
                <option value=""><?php _e( 'Select payment method', 'bdpay' ); ?></option>
                <?php foreach ( $enabled_methods as $method => $label ) : ?>
                    <option value="<?php echo esc_attr( $method ); ?>"><?php echo esc_html( $label ); ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div id="bdpay-method-info" style="display: none;">
            <?php foreach ( $enabled_methods as $method => $label ) : ?>
                <div class="bdpay-method-info" id="bdpay-info-<?php echo esc_attr( $method ); ?>" style="display: none;">
                    <?php $wallet_number = BDPay_Core::get_wallet_number( $method ); ?>
                    <?php if ( $wallet_number ) : ?>
                        <div class="bdpay-wallet-display">
                            <p><strong><?php printf( __( '%s Number:', 'bdpay' ), $label ); ?></strong> 
                            <span class="wallet-number"><?php echo esc_html( $wallet_number ); ?></span>
                            <button type="button" class="bdpay-copy-btn" data-copy="<?php echo esc_attr( $wallet_number ); ?>">
                                <?php _e( 'Copy', 'bdpay' ); ?>
                            </button></p>
                        </div>
                    <?php endif; ?>

                    <?php $instructions = BDPay_Core::get_instructions( $method ); ?>
                    <?php if ( $instructions ) : ?>
                        <div class="bdpay-method-instructions">
                            <?php echo wp_kses_post( wpautop( $instructions ) ); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="bdpay-form-row">
            <label for="bdpay_transaction_id"><?php _e( 'Transaction ID', 'bdpay' ); ?> <span class="required">*</span></label>
            <input type="text" name="transaction_id" id="bdpay_transaction_id" required 
                   placeholder="<?php _e( 'Enter the transaction ID from your mobile wallet', 'bdpay' ); ?>">
            <small class="bdpay-help-text"><?php _e( 'You can find this in your mobile wallet transaction history or SMS confirmation.', 'bdpay' ); ?></small>
        </div>

        <div class="bdpay-form-row">
            <label for="bdpay_sender_phone"><?php _e( 'Your Phone Number', 'bdpay' ); ?> <span class="required">*</span></label>
            <input type="tel" name="sender_phone" id="bdpay_sender_phone" required 
                   placeholder="<?php _e( 'Enter your mobile number (e.g., 01XXXXXXXXX)', 'bdpay' ); ?>">
            <small class="bdpay-help-text"><?php _e( 'Enter the phone number you used to send the payment.', 'bdpay' ); ?></small>
        </div>

        <div class="bdpay-form-row">
            <label for="bdpay_screenshot"><?php _e( 'Payment Screenshot', 'bdpay' ); ?> <span class="required">*</span></label>
            <input type="file" name="screenshot" id="bdpay_screenshot" accept="image/*" required>
            <small class="bdpay-help-text"><?php _e( 'Upload a screenshot of your payment confirmation from your mobile banking app. This is required for payment verification.', 'bdpay' ); ?></small>
        </div>

        <div class="bdpay-form-row">
            <label for="bdpay_notes"><?php _e( 'Additional Notes (Optional)', 'bdpay' ); ?></label>
            <textarea name="notes" id="bdpay_notes" rows="3" 
                      placeholder="<?php _e( 'Any additional information about your payment...', 'bdpay' ); ?>"></textarea>
        </div>

        <div class="bdpay-form-actions">
            <input type="hidden" name="order_id" value="<?php echo esc_attr( $order->get_id() ); ?>">
            <input type="hidden" name="action" value="bdpay_submit_payment">
            <input type="hidden" name="nonce" value="<?php echo wp_create_nonce( 'bdpay_frontend' ); ?>">
            
            <button type="submit" class="button bdpay-submit-btn">
                <?php _e( 'Submit Payment Details', 'bdpay' ); ?>
            </button>
        </div>
    </form>

    <div class="bdpay-security-notice">
        <p><small><?php _e( 'Your payment information is secure and will be verified by our team. You will receive an email confirmation once verified.', 'bdpay' ); ?></small></p>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Show/hide payment method info
    $('#bdpay_payment_method').on('change', function() {
        var selectedMethod = $(this).val();
        $('.bdpay-method-info').hide();
        $('#bdpay-method-info').hide();
        
        if (selectedMethod) {
            $('#bdpay-method-info').show();
            $('#bdpay-info-' + selectedMethod).show();
        }
    });

    // Copy wallet number functionality
    $('.bdpay-copy-btn').on('click', function() {
        var textToCopy = $(this).data('copy');
        navigator.clipboard.writeText(textToCopy).then(function() {
            alert('<?php _e( 'Wallet number copied to clipboard!', 'bdpay' ); ?>');
        });
    });

    // Form submission
    $('#bdpay-payment-submission').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('.bdpay-submit-btn');
        var formData = new FormData(this);
        
        // Disable submit button
        $submitBtn.prop('disabled', true).text('<?php _e( 'Submitting...', 'bdpay' ); ?>');
        
        $.ajax({
            url: '<?php echo admin_url( 'admin-ajax.php' ); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    if (response.data.redirect) {
                        window.location.href = response.data.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    alert('<?php _e( 'Error:', 'bdpay' ); ?> ' + response.data);
                    $submitBtn.prop('disabled', false).text('<?php _e( 'Submit Payment Details', 'bdpay' ); ?>');
                }
            },
            error: function() {
                alert('<?php _e( 'An error occurred. Please try again.', 'bdpay' ); ?>');
                $submitBtn.prop('disabled', false).text('<?php _e( 'Submit Payment Details', 'bdpay' ); ?>');
            }
        });
    });
});
</script>
