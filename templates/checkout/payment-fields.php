<?php
/**
 * BDPay payment fields template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="bdpay-payment-fields">
    <div class="bdpay-method-selection">
        <p class="bdpay-section-description"><?php _e( 'Select your preferred mobile banking service', 'bdpay' ); ?></p>

        <div class="bdpay-methods-list">
            <?php
            $method_count = count( $enabled_methods );
            $is_first = true;
            foreach ( $enabled_methods as $method => $label ) :
                $auto_checked = ( $method_count === 1 ) ? 'checked' : '';
            ?>
                <div class="bdpay-method-option">
                    <input type="radio" name="bdpay_payment_method" value="<?php echo esc_attr( $method ); ?>" id="bdpay_method_<?php echo esc_attr( $method ); ?>" required <?php echo $auto_checked; ?>>
                    <label for="bdpay_method_<?php echo esc_attr( $method ); ?>" class="bdpay-method-label">
                        <?php echo esc_html( $label ); ?>
                    </label>
                </div>
            <?php
                $is_first = false;
            endforeach;
            ?>
        </div>
    </div>

    <?php foreach ( $enabled_methods as $method => $label ) : ?>
        <div class="bdpay-method-details" id="bdpay-details-<?php echo esc_attr( $method ); ?>" style="display: none;">
            <div class="bdpay-payment-info">
                <h4><?php printf( __( '%s Payment Details', 'bdpay' ), $label ); ?></h4>

                <?php $wallet_number = BDPay_Core::get_wallet_number( $method ); ?>
                <?php if ( $wallet_number ) : ?>
                    <div class="bdpay-wallet-info">
                        <label><?php _e( 'Send money to:', 'bdpay' ); ?></label>
                        <div class="bdpay-wallet-display">
                            <span class="bdpay-wallet-number"><?php echo esc_html( $wallet_number ); ?></span>
                            <button type="button" class="bdpay-copy-btn" data-copy="<?php echo esc_attr( $wallet_number ); ?>" title="<?php _e( 'Copy wallet number', 'bdpay' ); ?>">
                                📋 
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="bdpay-amount-info">
                    <label><?php _e( 'Amount to send:', 'bdpay' ); ?></label>
                    <div class="bdpay-amount-display">
                        <?php echo wc_price( WC()->cart->get_total( 'edit' ) ); ?>
                    </div>
                </div>

                <?php $instructions = BDPay_Core::get_instructions( $method ); ?>
                <?php if ( $instructions ) : ?>
                    <div class="bdpay-instructions">
                        <label><?php _e( 'Instructions:', 'bdpay' ); ?></label>
                        <div class="bdpay-instructions-text">
                            <?php echo wp_kses_post( wpautop( $instructions ) ); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Payment Submission Fields -->
    <div class="bdpay-payment-submission" id="bdpay-payment-submission" style="display: none;">
        <h4><?php _e( 'Payment Confirmation', 'bdpay' ); ?></h4>
        <p><?php _e( 'After sending the money, please provide your transaction details below', 'bdpay' ); ?></p>

        <div class="bdpay-form-fields">
            <div class="bdpay-transaction-id">
                <label><?php _e( 'Transaction ID', 'bdpay' ); ?> <span class="required">*</span></label>
                <div class="bdpay-transaction-id-input">
                    <input type="text" name="bdpay_transaction_id" id="bdpay_transaction_id"
                           required placeholder="<?php _e( 'Enter transaction ID', 'bdpay' ); ?>">
                </div>
                <small><?php _e( 'Find this in your mobile wallet transaction history or SMS', 'bdpay' ); ?></small>
            </div>

            <div class="bdpay-sender-phone">
                <label><?php _e( 'Your Phone Number', 'bdpay' ); ?> <span class="required">*</span></label>
                <div class="bdpay-sender-phone-input">
                    <input type="tel" name="bdpay_sender_phone" id="bdpay_sender_phone"
                           required placeholder="<?php _e( '01XXXXXXXXX', 'bdpay' ); ?>">
                </div>
                <small><?php _e( 'The phone number you used to send the payment', 'bdpay' ); ?></small>
            </div>

            <!-- Screenshot upload temporarily disabled -->
            <div class="bdpay-screenshot" style="display: none;">
                <label><?php _e( 'Payment Screenshot (Coming Soon)', 'bdpay' ); ?></label>
                <input type="file" name="bdpay_screenshot" id="bdpay_screenshot" accept="image/*" disabled>
            </div>

            <div class="bdpay-notes">
                <label><?php _e( 'Additional Notes (Optional)', 'bdpay' ); ?></label>
                <div class="bdpay-notes-input">
                    <textarea name="bdpay_notes" id="bdpay_notes" rows="3"
                              placeholder="<?php _e( 'Any additional information about your payment...', 'bdpay' ); ?>"></textarea>
                </div>
            </div>
        </div>
    </div>

    <div class="bdpay-notice">
        <p><strong><?php esc_html_e( 'Important:', 'bdpay' ); ?></strong> <?php esc_html_e( 'Please complete the payment first, then fill in your transaction details above before placing the order.', 'bdpay' ); ?></p>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Ensure payment submission fields are shown when a method is selected
    function showPaymentFields() {
        var $checkedMethod = $('input[name="bdpay_payment_method"]:checked');
        if ($checkedMethod.length > 0) {
            var selectedMethod = $checkedMethod.val();
            console.log('BDPay Template: Showing fields for method:', selectedMethod);

            // Show method details
            $('.bdpay-method-details').hide();
            $('#bdpay-details-' + selectedMethod).show();

            // Show payment submission form
            $('#bdpay-payment-submission').show();
        }
    }

    // Handle payment method changes
    $('input[name="bdpay_payment_method"]').on('change', function() {
        showPaymentFields();
    });

    // Initialize on load
    setTimeout(showPaymentFields, 100);
    setTimeout(showPaymentFields, 500);
});
</script>


