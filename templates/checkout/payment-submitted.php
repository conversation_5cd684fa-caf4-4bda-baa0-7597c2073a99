<?php
/**
 * BDPay payment submitted template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="bdpay-payment-submitted">
    <div class="bdpay-success-icon">
        <span class="dashicons dashicons-yes-alt"></span>
    </div>
    
    <h3><?php _e( 'Payment Details Submitted Successfully!', 'bdpay' ); ?></h3>
    
    <div class="bdpay-submission-details">
        <h4><?php _e( 'Submitted Information:', 'bdpay' ); ?></h4>
        
        <table class="bdpay-details-table">
            <tr>
                <td><strong><?php _e( 'Payment Method:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( BDPay_Core::get_payment_methods()[ $transaction->get_payment_method() ] ?? $transaction->get_payment_method() ); ?></td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Transaction ID:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( $transaction->get_transaction_id() ); ?></td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Sender Phone:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( $transaction->get_sender_phone() ); ?></td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Amount:', 'bdpay' ); ?></strong></td>
                <td><?php echo wc_price( $transaction->get_amount() ); ?></td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Status:', 'bdpay' ); ?></strong></td>
                <td>
                    <span class="bdpay-status bdpay-status-<?php echo esc_attr( $transaction->get_status() ); ?>">
                        <?php 
                        switch ( $transaction->get_status() ) {
                            case 'submitted':
                                _e( 'Submitted - Awaiting Verification', 'bdpay' );
                                break;
                            case 'verified':
                                _e( 'Verified - Payment Confirmed', 'bdpay' );
                                break;
                            case 'rejected':
                                _e( 'Rejected - Please Contact Support', 'bdpay' );
                                break;
                            default:
                                echo esc_html( ucfirst( $transaction->get_status() ) );
                        }
                        ?>
                    </span>
                </td>
            </tr>
            <?php if ( $transaction->get_screenshot_url() ) : ?>
            <tr>
                <td><strong><?php _e( 'Screenshot:', 'bdpay' ); ?></strong></td>
                <td><a href="<?php echo esc_url( $transaction->get_screenshot_url() ); ?>" target="_blank"><?php _e( 'View Screenshot', 'bdpay' ); ?></a></td>
            </tr>
            <?php endif; ?>
            <?php if ( $transaction->get_notes() ) : ?>
            <tr>
                <td><strong><?php _e( 'Notes:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( $transaction->get_notes() ); ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td><strong><?php _e( 'Submitted On:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $transaction->get_date_created() ) ) ); ?></td>
            </tr>
        </table>
    </div>

    <div class="bdpay-next-steps">
        <h4><?php _e( 'What happens next?', 'bdpay' ); ?></h4>
        <ul>
            <li><?php _e( 'Our team will verify your payment details within 24 hours.', 'bdpay' ); ?></li>
            <li><?php _e( 'You will receive an email confirmation once your payment is verified.', 'bdpay' ); ?></li>
            <li><?php _e( 'Your order will be processed and shipped after payment verification.', 'bdpay' ); ?></li>
        </ul>
    </div>

    <div class="bdpay-actions">
        <a href="<?php echo esc_url( $order->get_view_order_url() ); ?>" class="button">
            <?php _e( 'View Order Details', 'bdpay' ); ?>
        </a>
        
        <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="button">
            <?php _e( 'Continue Shopping', 'bdpay' ); ?>
        </a>
    </div>

    <?php if ( 'rejected' === $transaction->get_status() ) : ?>
    <div class="bdpay-rejected-notice">
        <p><strong><?php _e( 'Payment Rejected:', 'bdpay' ); ?></strong></p>
        <p><?php _e( 'Your payment could not be verified. Please check your transaction details and try again, or contact our support team for assistance.', 'bdpay' ); ?></p>
        
        <a href="<?php echo esc_url( add_query_arg( 'bdpay_resubmit', '1', $order->get_view_order_url() ) ); ?>" class="button">
            <?php _e( 'Resubmit Payment Details', 'bdpay' ); ?>
        </a>
    </div>
    <?php endif; ?>

    <div class="bdpay-support-info">
        <p><small>
            <?php _e( 'Need help? Contact our support team at', 'bdpay' ); ?> 
            <a href="mailto:<?php echo esc_attr( get_option( 'admin_email' ) ); ?>"><?php echo esc_html( get_option( 'admin_email' ) ); ?></a>
        </small></p>
    </div>
</div>

<style>
.bdpay-payment-submitted {
    text-align: center;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
}

.bdpay-success-icon .dashicons {
    font-size: 48px;
    color: #46b450;
    margin-bottom: 10px;
}

.bdpay-details-table {
    width: 100%;
    margin: 20px 0;
    border-collapse: collapse;
}

.bdpay-details-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.bdpay-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.bdpay-status-submitted {
    background: #fff3cd;
    color: #856404;
}

.bdpay-status-verified {
    background: #d4edda;
    color: #155724;
}

.bdpay-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.bdpay-next-steps ul {
    text-align: left;
    display: inline-block;
    margin: 0 auto;
}

.bdpay-actions {
    margin: 20px 0;
}

.bdpay-actions .button {
    margin: 0 5px;
}

.bdpay-rejected-notice {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.bdpay-support-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}
</style>
