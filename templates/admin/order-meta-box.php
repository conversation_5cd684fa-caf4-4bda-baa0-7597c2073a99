<?php
/**
 * BDPay order meta box template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="bdpay-order-meta-box">
    <div class="bdpay-transaction-details">
        <h4><?php _e( 'Payment Information', 'bdpay' ); ?></h4>
        
        <table class="bdpay-meta-table">
            <tr>
                <td><strong><?php _e( 'Payment Method:', 'bdpay' ); ?></strong></td>
                <td>
                    <span class="bdpay-method-badge bdpay-method-<?php echo esc_attr( $transaction->get_payment_method() ); ?>">
                        <?php echo esc_html( BDPay_Core::get_payment_methods()[ $transaction->get_payment_method() ] ?? $transaction->get_payment_method() ); ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Transaction ID:', 'bdpay' ); ?></strong></td>
                <td>
                    <code><?php echo esc_html( $transaction->get_transaction_id() ); ?></code>
                    <button type="button" class="button-secondary bdpay-copy-btn" data-copy="<?php echo esc_attr( $transaction->get_transaction_id() ); ?>">
                        <?php _e( 'Copy', 'bdpay' ); ?>
                    </button>
                </td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Sender Phone:', 'bdpay' ); ?></strong></td>
                <td>
                    <code><?php echo esc_html( $transaction->get_sender_phone() ); ?></code>
                    <button type="button" class="button-secondary bdpay-copy-btn" data-copy="<?php echo esc_attr( $transaction->get_sender_phone() ); ?>">
                        <?php _e( 'Copy', 'bdpay' ); ?>
                    </button>
                </td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Amount:', 'bdpay' ); ?></strong></td>
                <td><?php echo wc_price( $transaction->get_amount() ); ?></td>
            </tr>
            <tr>
                <td><strong><?php _e( 'Status:', 'bdpay' ); ?></strong></td>
                <td>
                    <span class="bdpay-status bdpay-status-<?php echo esc_attr( $transaction->get_status() ); ?>">
                        <?php 
                        switch ( $transaction->get_status() ) {
                            case 'pending':
                                _e( 'Pending Submission', 'bdpay' );
                                break;
                            case 'submitted':
                                _e( 'Submitted - Awaiting Verification', 'bdpay' );
                                break;
                            case 'verified':
                                _e( 'Verified - Payment Confirmed', 'bdpay' );
                                break;
                            case 'rejected':
                                _e( 'Rejected', 'bdpay' );
                                break;
                            default:
                                echo esc_html( ucfirst( $transaction->get_status() ) );
                        }
                        ?>
                    </span>
                </td>
            </tr>
            <?php if ( $transaction->get_screenshot_url() ) : ?>
            <tr>
                <td><strong><?php _e( 'Screenshot:', 'bdpay' ); ?></strong></td>
                <td>
                    <a href="<?php echo esc_url( $transaction->get_screenshot_url() ); ?>" target="_blank" class="button-secondary">
                        <?php _e( 'View Screenshot', 'bdpay' ); ?>
                    </a>
                </td>
            </tr>
            <?php endif; ?>
            <?php if ( $transaction->get_notes() ) : ?>
            <tr>
                <td><strong><?php _e( 'Customer Notes:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( $transaction->get_notes() ); ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td><strong><?php _e( 'Submitted On:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $transaction->get_date_created() ) ) ); ?></td>
            </tr>
            <?php if ( $transaction->get_date_modified() !== $transaction->get_date_created() ) : ?>
            <tr>
                <td><strong><?php _e( 'Last Modified:', 'bdpay' ); ?></strong></td>
                <td><?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $transaction->get_date_modified() ) ) ); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

    <?php if ( in_array( $transaction->get_status(), array( 'submitted', 'rejected' ) ) ) : ?>
    <div class="bdpay-verification-actions">
        <h4><?php _e( 'Verification Actions', 'bdpay' ); ?></h4>
        
        <div class="bdpay-action-buttons">
            <?php if ( 'submitted' === $transaction->get_status() || 'rejected' === $transaction->get_status() ) : ?>
                <button type="button" class="button-primary bdpay-verify-btn" 
                        data-order-id="<?php echo esc_attr( $order->get_id() ); ?>" 
                        data-action="verify">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e( 'Verify Payment', 'bdpay' ); ?>
                </button>
            <?php endif; ?>
            
            <?php if ( 'submitted' === $transaction->get_status() ) : ?>
                <button type="button" class="button-secondary bdpay-reject-btn" 
                        data-order-id="<?php echo esc_attr( $order->get_id() ); ?>" 
                        data-action="reject">
                    <span class="dashicons dashicons-no"></span>
                    <?php _e( 'Reject Payment', 'bdpay' ); ?>
                </button>
            <?php endif; ?>
        </div>

        <div class="bdpay-verification-tips">
            <h5><?php _e( 'Verification Tips:', 'bdpay' ); ?></h5>
            <ul>
                <li><?php _e( 'Check your mobile wallet transaction history for the provided transaction ID.', 'bdpay' ); ?></li>
                <li><?php _e( 'Verify the sender phone number matches the transaction.', 'bdpay' ); ?></li>
                <li><?php _e( 'Confirm the amount received matches the order total.', 'bdpay' ); ?></li>
                <li><?php _e( 'Review any uploaded screenshot for additional verification.', 'bdpay' ); ?></li>
            </ul>
        </div>
    </div>
    <?php endif; ?>

    <?php if ( 'verified' === $transaction->get_status() ) : ?>
    <div class="bdpay-verified-notice">
        <p><span class="dashicons dashicons-yes-alt"></span> <?php _e( 'This payment has been verified and confirmed.', 'bdpay' ); ?></p>
    </div>
    <?php endif; ?>

    <?php if ( 'rejected' === $transaction->get_status() ) : ?>
    <div class="bdpay-rejected-notice">
        <p><span class="dashicons dashicons-warning"></span> <?php _e( 'This payment has been rejected. The customer has been notified.', 'bdpay' ); ?></p>
    </div>
    <?php endif; ?>
</div>

<style>
.bdpay-order-meta-box {
    padding: 10px 0;
}

.bdpay-meta-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.bdpay-meta-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.bdpay-meta-table td:first-child {
    width: 30%;
    background: #f9f9f9;
}

.bdpay-method-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #0073aa;
    color: white;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.bdpay-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.bdpay-status-pending {
    background: #f0f0f1;
    color: #646970;
}

.bdpay-status-submitted {
    background: #fff3cd;
    color: #856404;
}

.bdpay-status-verified {
    background: #d4edda;
    color: #155724;
}

.bdpay-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.bdpay-copy-btn {
    margin-left: 5px;
    font-size: 11px;
    padding: 2px 6px;
    height: auto;
    line-height: 1.2;
}

.bdpay-verification-actions {
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.bdpay-action-buttons {
    margin-bottom: 15px;
}

.bdpay-action-buttons .button {
    margin-right: 10px;
}

.bdpay-action-buttons .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

.bdpay-verification-tips ul {
    margin: 10px 0;
    padding-left: 20px;
}

.bdpay-verification-tips li {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
}

.bdpay-verified-notice,
.bdpay-rejected-notice {
    padding: 10px;
    border-radius: 3px;
    margin-top: 15px;
}

.bdpay-verified-notice {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.bdpay-rejected-notice {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.bdpay-verified-notice .dashicons,
.bdpay-rejected-notice .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Copy functionality
    $('.bdpay-copy-btn').on('click', function() {
        var textToCopy = $(this).data('copy');
        navigator.clipboard.writeText(textToCopy).then(function() {
            alert('<?php _e( 'Copied to clipboard!', 'bdpay' ); ?>');
        });
    });

    // Verification actions
    $('.bdpay-verify-btn, .bdpay-reject-btn').on('click', function() {
        var $btn = $(this);
        var orderId = $btn.data('order-id');
        var action = $btn.data('action');
        var confirmMessage = action === 'verify' ? 
            '<?php _e( 'Are you sure you want to verify this payment?', 'bdpay' ); ?>' : 
            '<?php _e( 'Are you sure you want to reject this payment?', 'bdpay' ); ?>';

        if (!confirm(confirmMessage)) {
            return;
        }

        $btn.prop('disabled', true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'bdpay_verify_payment',
                order_id: orderId,
                action_type: action,
                nonce: '<?php echo wp_create_nonce( 'bdpay_verify_payment' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('<?php _e( 'Error:', 'bdpay' ); ?> ' + response.data);
                    $btn.prop('disabled', false);
                }
            },
            error: function() {
                alert('<?php _e( 'An error occurred. Please try again.', 'bdpay' ); ?>');
                $btn.prop('disabled', false);
            }
        });
    });
});
</script>
