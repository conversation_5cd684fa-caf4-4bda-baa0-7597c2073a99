<?php
/**
 * BDPay admin transactions page template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="wrap bdpay-admin-wrap">
    <div class="bdpay-admin-header">
        <div class="bdpay-header-content">
            <h1 class="bdpay-page-title">
                <span class="bdpay-title-icon">💳</span>
                <?php _e( 'BDPay Transactions', 'bdpay' ); ?>
            </h1>
            <p class="bdpay-page-subtitle"><?php _e( 'Manage and verify mobile payment transactions', 'bdpay' ); ?></p>
        </div>
        <div class="bdpay-header-actions">
            <button type="button" class="button button-primary bdpay-refresh-btn">
                <span class="dashicons dashicons-update"></span>
                <?php _e( 'Refresh', 'bdpay' ); ?>
            </button>
        </div>
    </div>

    <?php if ( empty( $transactions ) ) : ?>
        <div class="bdpay-empty-state">
            <div class="bdpay-empty-icon">📊</div>
            <h3><?php _e( 'No Transactions Yet', 'bdpay' ); ?></h3>
            <p><?php _e( 'When customers make payments using BDPay, their transactions will appear here for verification.', 'bdpay' ); ?></p>
        </div>
    <?php else : ?>
        <div class="bdpay-transactions-container">
            <div class="bdpay-table-header">
                <div class="bdpay-table-title">
                    <h2><?php _e( 'Recent Transactions', 'bdpay' ); ?></h2>
                    <span class="bdpay-transaction-count"><?php printf( _n( '%d transaction', '%d transactions', count( $transactions ), 'bdpay' ), count( $transactions ) ); ?></span>
                </div>
                <div class="bdpay-table-filters">
                    <select class="bdpay-status-filter">
                        <option value=""><?php _e( 'All Statuses', 'bdpay' ); ?></option>
                        <option value="pending"><?php _e( 'Pending', 'bdpay' ); ?></option>
                        <option value="submitted"><?php _e( 'Submitted', 'bdpay' ); ?></option>
                        <option value="verified"><?php _e( 'Verified', 'bdpay' ); ?></option>
                        <option value="rejected"><?php _e( 'Rejected', 'bdpay' ); ?></option>
                    </select>
                </div>
            </div>

            <div class="bdpay-modern-table">
                <table class="bdpay-transactions-table">
                    <thead>
                        <tr>
                            <th class="bdpay-col-order"><?php _e( 'Order', 'bdpay' ); ?></th>
                            <th class="bdpay-col-transaction"><?php _e( 'Transaction Details', 'bdpay' ); ?></th>
                            <th class="bdpay-col-method"><?php _e( 'Method', 'bdpay' ); ?></th>
                            <th class="bdpay-col-amount"><?php _e( 'Amount', 'bdpay' ); ?></th>
                            <th class="bdpay-col-status"><?php _e( 'Status', 'bdpay' ); ?></th>
                            <th class="bdpay-col-date"><?php _e( 'Date', 'bdpay' ); ?></th>
                            <th class="bdpay-col-actions"><?php _e( 'Actions', 'bdpay' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $transactions as $transaction ) : ?>
                            <?php
                            $order = wc_get_order( $transaction->get_order_id() );
                            $payment_methods = BDPay_Core::get_payment_methods();
                            $method_label = isset( $payment_methods[ $transaction->get_payment_method() ] )
                                ? $payment_methods[ $transaction->get_payment_method() ]
                                : $transaction->get_payment_method();
                            $status = $transaction->get_status();
                            ?>
                            <tr class="bdpay-transaction-row" data-status="<?php echo esc_attr( $status ); ?>">
                                <td class="bdpay-col-order">
                                    <div class="bdpay-order-info">
                                        <?php if ( $order ) : ?>
                                            <div class="bdpay-order-number">
                                                <a href="<?php echo esc_url( admin_url( 'post.php?post=' . $order->get_id() . '&action=edit' ) ); ?>" class="bdpay-order-link">
                                                    #<?php echo esc_html( $order->get_order_number() ); ?>
                                                </a>
                                            </div>
                                            <div class="bdpay-customer-name">
                                                <?php echo esc_html( $order->get_billing_first_name() . ' ' . $order->get_billing_last_name() ); ?>
                                            </div>
                                        <?php else : ?>
                                            <div class="bdpay-order-error">
                                                <span class="dashicons dashicons-warning"></span>
                                                #<?php echo esc_html( $transaction->get_order_id() ); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="bdpay-col-transaction">
                                    <div class="bdpay-transaction-details">
                                        <div class="bdpay-transaction-id">
                                            <strong><?php _e( 'ID:', 'bdpay' ); ?></strong>
                                            <code><?php echo esc_html( $transaction->get_transaction_id() ?: __( 'Not provided', 'bdpay' ) ); ?></code>
                                        </div>
                                        <div class="bdpay-sender-phone">
                                            <strong><?php _e( 'Phone:', 'bdpay' ); ?></strong>
                                            <?php echo esc_html( $transaction->get_sender_phone() ?: __( 'Not provided', 'bdpay' ) ); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="bdpay-col-method">
                                    <div class="bdpay-method-display">
                                        <span class="bdpay-method-badge bdpay-method-<?php echo esc_attr( $transaction->get_payment_method() ); ?>">
                                            <?php echo esc_html( $method_label ); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="bdpay-col-amount">
                                    <div class="bdpay-amount-display">
                                        <?php if ( $order ) : ?>
                                            <?php echo wp_kses_post( wc_price( $transaction->get_amount(), array( 'currency' => $order->get_currency() ) ) ); ?>
                                        <?php else : ?>
                                            <?php echo esc_html( number_format( $transaction->get_amount(), 2 ) ); ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="bdpay-col-status">
                                    <?php
                                    $status_labels = array(
                                        'pending'   => __( 'Pending', 'bdpay' ),
                                        'submitted' => __( 'Submitted', 'bdpay' ),
                                        'verified'  => __( 'Verified', 'bdpay' ),
                                        'rejected'  => __( 'Rejected', 'bdpay' ),
                                    );
                                    $status_label = isset( $status_labels[ $status ] ) ? $status_labels[ $status ] : ucfirst( $status );
                                    $status_icons = array(
                                        'pending'   => '⏳',
                                        'submitted' => '📝',
                                        'verified'  => '✅',
                                        'rejected'  => '❌',
                                    );
                                    $status_icon = isset( $status_icons[ $status ] ) ? $status_icons[ $status ] : '📄';
                                    ?>
                                    <div class="bdpay-status-display">
                                        <span class="bdpay-status-badge bdpay-status-<?php echo esc_attr( $status ); ?>">
                                            <span class="bdpay-status-icon"><?php echo $status_icon; ?></span>
                                            <?php echo esc_html( $status_label ); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="bdpay-col-date">
                                    <div class="bdpay-date-display">
                                        <div class="bdpay-date">
                                            <?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $transaction->get_date_created() ) ) ); ?>
                                        </div>
                                        <div class="bdpay-time">
                                            <?php echo esc_html( date_i18n( get_option( 'time_format' ), strtotime( $transaction->get_date_created() ) ) ); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="bdpay-col-actions">
                                    <div class="bdpay-actions-container">
                                        <?php if ( 'submitted' === $status ) : ?>
                                            <div class="bdpay-primary-actions">
                                                <button type="button" class="button button-primary button-small bdpay-verify-payment"
                                                        data-order-id="<?php echo esc_attr( $transaction->get_order_id() ); ?>"
                                                        data-action="verify">
                                                    <span class="dashicons dashicons-yes"></span>
                                                    <?php _e( 'Verify', 'bdpay' ); ?>
                                                </button>
                                                <button type="button" class="button button-secondary button-small bdpay-verify-payment"
                                                        data-order-id="<?php echo esc_attr( $transaction->get_order_id() ); ?>"
                                                        data-action="reject">
                                                    <span class="dashicons dashicons-no"></span>
                                                    <?php _e( 'Reject', 'bdpay' ); ?>
                                                </button>
                                            </div>
                                        <?php endif; ?>

                                        <div class="bdpay-secondary-actions">
                                            <?php if ( $order ) : ?>
                                                <a href="<?php echo esc_url( admin_url( 'post.php?post=' . $order->get_id() . '&action=edit' ) ); ?>"
                                                   class="button button-small">
                                                    <span class="dashicons dashicons-edit"></span>
                                                    <?php _e( 'View Order', 'bdpay' ); ?>
                                                </a>
                                            <?php endif; ?>

                                            <?php if ( $transaction->get_screenshot_url() ) : ?>
                                                <a href="<?php echo esc_url( $transaction->get_screenshot_url() ); ?>"
                                                   target="_blank" class="button button-small">
                                                    <span class="dashicons dashicons-camera"></span>
                                                    <?php _e( 'Screenshot', 'bdpay' ); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ( $transaction->get_notes() ) : ?>
                                            <div class="bdpay-transaction-notes">
                                                <div class="bdpay-notes-toggle" title="<?php _e( 'View Notes', 'bdpay' ); ?>">
                                                    <span class="dashicons dashicons-admin-comments"></span>
                                                </div>
                                                <div class="bdpay-notes-content" style="display: none;">
                                                    <?php echo esc_html( $transaction->get_notes() ); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<style>
/* Modern Admin Styles */
.bdpay-admin-wrap {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.bdpay-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    padding: 24px 0;
    border-bottom: 1px solid #e1e5e9;
}

.bdpay-page-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: #1d2327;
    line-height: 1.2;
}

.bdpay-title-icon {
    font-size: 32px;
}

.bdpay-page-subtitle {
    margin: 0;
    color: #646970;
    font-size: 14px;
}

.bdpay-refresh-btn {
    background: #d61369 !important;
    border: 0 !important;
    padding: 10px 50px !important;
    display: flex !important;
    border-radius: 4px !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    align-items: center !important;
    gap: 8px !important;
    transition: background-color 0.2s ease !important;
}

.bdpay-refresh-btn:hover {
    background: #b8115a !important;
    color: #ffffff !important;
}

.bdpay-refresh-btn:focus {
    background: #b8115a !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 2px rgba(214, 19, 105, 0.3) !important;
}

/* Empty State */
.bdpay-empty-state {
    text-align: center;
    padding: 80px 20px;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
}

.bdpay-empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
}

.bdpay-empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    color: #1d2327;
}

.bdpay-empty-state p {
    margin: 0;
    color: #646970;
    max-width: 400px;
    margin: 0 auto;
}

/* Transactions Container */
.bdpay-transactions-container {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.bdpay-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f6f7f7;
    border-bottom: 1px solid #e1e5e9;
}

.bdpay-table-title h2 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.bdpay-transaction-count {
    font-size: 13px;
    color: #646970;
}

.bdpay-status-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

/* Modern Table */
.bdpay-modern-table {
    overflow-x: auto;
}

.bdpay-transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.bdpay-transactions-table th {
    padding: 16px 20px;
    background: #f9f9f9;
    border-bottom: 1px solid #e1e5e9;
    font-size: 13px;
    font-weight: 600;
    color: #646970;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bdpay-transaction-row {
    border-bottom: 1px solid #f0f0f1;
    transition: background-color 0.2s ease;
}

.bdpay-transaction-row:hover {
    background-color: #f9f9f9;
}

.bdpay-transactions-table td {
    padding: 20px;
    vertical-align: top;
}

/* Column Styles */
.bdpay-order-info {
    min-width: 140px;
}

.bdpay-order-link {
    font-weight: 600;
    color: #2271b1;
    text-decoration: none;
}

.bdpay-customer-name {
    font-size: 13px;
    color: #646970;
    margin-top: 4px;
}

.bdpay-transaction-details {
    min-width: 180px;
}

.bdpay-transaction-id,
.bdpay-sender-phone {
    margin-bottom: 4px;
    font-size: 13px;
}

.bdpay-transaction-id code {
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.bdpay-method-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bdpay-method-bkash { background: linear-gradient(135deg, #e2136e, #c41162); }
.bdpay-method-nagad { background: linear-gradient(135deg, #f47920, #e06b1a); }
.bdpay-method-rocket { background: linear-gradient(135deg, #8b1538, #7a1230); }
.bdpay-method-upay { background: linear-gradient(135deg, #ff6b35, #e55a2e); }
.bdpay-method-surecash { background: linear-gradient(135deg, #1e88e5, #1976d2); }

.bdpay-amount-display {
    font-weight: 600;
    font-size: 16px;
    color: #1d2327;
}

.bdpay-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bdpay-status-pending { background: #fef3c7; color: #92400e; }
.bdpay-status-submitted { background: #dbeafe; color: #1e40af; }
.bdpay-status-verified { background: #d1fae5; color: #065f46; }
.bdpay-status-rejected { background: #fee2e2; color: #991b1b; }

.bdpay-date-display {
    min-width: 100px;
}

.bdpay-date {
    font-weight: 500;
    color: #1d2327;
}

.bdpay-time {
    font-size: 13px;
    color: #646970;
    margin-top: 2px;
}

.bdpay-actions-container {
    min-width: 200px;
}

.bdpay-primary-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.bdpay-secondary-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

/* Action Buttons Styling */
.bdpay-secondary-actions .button {
    background: #0073aa !important;
    border: 1px solid #0073aa !important;
    color: #ffffff !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: all 0.2s ease !important;
    min-height: auto !important;
    line-height: 1.2 !important;
}

.bdpay-secondary-actions .button:hover {
    background: #005a87 !important;
    border-color: #005a87 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.3) !important;
}

.bdpay-secondary-actions .button:focus {
    background: #005a87 !important;
    border-color: #005a87 !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.3) !important;
}

.bdpay-secondary-actions .button .dashicons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    margin: 0 !important;
}

.bdpay-transaction-notes {
    margin-top: 8px;
    position: relative;
}

.bdpay-notes-toggle {
    cursor: pointer;
    color: #646970;
    padding: 4px;
    border-radius: 3px;
    transition: color 0.2s ease;
}

.bdpay-notes-toggle:hover {
    color: #2271b1;
}

.bdpay-notes-content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 10;
}

/* Responsive */
@media (max-width: 1200px) {
    .bdpay-admin-header {
        flex-direction: column;
        gap: 16px;
    }

    .bdpay-table-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Modern payment verification with better UX
    $('.bdpay-verify-payment').on('click', function(e) {
        e.preventDefault();

        var $this = $(this);
        var orderId = $this.data('order-id');
        var action = $this.data('action');
        var isVerify = action === 'verify';
        var confirmMessage = isVerify
            ? '<?php echo esc_js( __( 'Are you sure you want to verify this payment?', 'bdpay' ) ); ?>'
            : '<?php echo esc_js( __( 'Are you sure you want to reject this payment?', 'bdpay' ) ); ?>';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Store original content
        var originalHtml = $this.html();

        // Show loading state
        $this.prop('disabled', true)
             .html('<span class="dashicons dashicons-update spin"></span> <?php echo esc_js( __( 'Processing...', 'bdpay' ) ); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'bdpay_verify_payment',
                order_id: orderId,
                action_type: action,
                nonce: '<?php echo wp_create_nonce( 'bdpay_verify_payment' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showNotification(response.data, 'success');

                    // Update the row status
                    var $row = $this.closest('.bdpay-transaction-row');
                    var newStatus = isVerify ? 'verified' : 'rejected';
                    var newStatusLabel = isVerify ? '<?php echo esc_js( __( 'Verified', 'bdpay' ) ); ?>' : '<?php echo esc_js( __( 'Rejected', 'bdpay' ) ); ?>';
                    var newStatusIcon = isVerify ? '✅' : '❌';

                    $row.attr('data-status', newStatus);
                    $row.find('.bdpay-status-badge')
                        .removeClass('bdpay-status-pending bdpay-status-submitted bdpay-status-verified bdpay-status-rejected')
                        .addClass('bdpay-status-' + newStatus)
                        .html('<span class="bdpay-status-icon">' + newStatusIcon + '</span>' + newStatusLabel);

                    // Remove action buttons
                    $this.closest('.bdpay-primary-actions').fadeOut(300);

                } else {
                    showNotification('<?php echo esc_js( __( 'Error:', 'bdpay' ) ); ?> ' + response.data, 'error');
                    $this.prop('disabled', false).html(originalHtml);
                }
            },
            error: function() {
                showNotification('<?php echo esc_js( __( 'An error occurred. Please try again.', 'bdpay' ) ); ?>', 'error');
                $this.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Notes toggle functionality
    $('.bdpay-notes-toggle').on('click', function() {
        var $content = $(this).siblings('.bdpay-notes-content');
        $content.toggle();
    });

    // Status filter functionality
    $('.bdpay-status-filter').on('change', function() {
        var selectedStatus = $(this).val();
        var $rows = $('.bdpay-transaction-row');

        if (selectedStatus === '') {
            $rows.show();
        } else {
            $rows.hide();
            $rows.filter('[data-status="' + selectedStatus + '"]').show();
        }
    });

    // Refresh button functionality
    $('.bdpay-refresh-btn').on('click', function() {
        location.reload();
    });

    // Modern notification system
    function showNotification(message, type) {
        var notificationClass = type === 'success' ? 'notice-success' : 'notice-error';
        var $notification = $('<div class="notice ' + notificationClass + ' is-dismissible"><p>' + message + '</p></div>');

        $('.bdpay-admin-header').after($notification);

        // Auto dismiss after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Add spinning animation for loading states
    $('<style>.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>').appendTo('head');
});
</script>
