# BDPay - Manual Mobile Payment Gateway

[![WordPress Plugin Version](https://img.shields.io/wordpress/plugin/v/bdpay.svg)](https://wordpress.org/plugins/bdpay/)
[![WordPress Plugin Downloads](https://img.shields.io/wordpress/plugin/dt/bdpay.svg)](https://wordpress.org/plugins/bdpay/)
[![WordPress Plugin Rating](https://img.shields.io/wordpress/plugin/r/bdpay.svg)](https://wordpress.org/plugins/bdpay/)
[![License](https://img.shields.io/badge/license-GPL%20v2%2B-blue.svg)](https://www.gnu.org/licenses/gpl-2.0.html)

Manual mobile payment gateway for Bangladesh supporting bKash, Nagad, Rocket, Upay, and SureCash. Perfect for WooCommerce stores accepting manual MFS payments.

## 🚀 Features

### 💳 Payment Methods
- **bKash** - Bangladesh's leading mobile financial service
- **Nagad** - Digital financial service by Bangladesh Post Office
- **Rocket** - Mobile financial service by Dutch-Bangla Bank
- **Upay** - Mobile financial service by UCB Fintech
- **SureCash** - Mobile financial service by Sure Cash

### 🛒 WooCommerce Integration
- Seamless checkout experience
- Custom order statuses
- Order management integration
- Payment verification workflow

### 🔧 Admin Features
- Comprehensive settings panel
- Transaction management dashboard
- Payment verification interface
- Email notification system
- Bulk actions support

### 🔒 Security
- Data sanitization and validation
- Nonce verification
- Rate limiting
- Secure file uploads
- SQL injection prevention
- XSS protection

### 🌐 Internationalization
- Translation ready
- Bangla language included
- RTL support ready
- .pot file provided

## 📋 Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- WooCommerce 3.0 or higher
- MySQL 5.6 or higher

## 🔧 Installation

### Automatic Installation

1. Log in to your WordPress admin panel
2. Go to **Plugins > Add New**
3. Search for "BDPay"
4. Click **Install Now** and then **Activate**

### Manual Installation

1. Download the plugin zip file
2. Upload to `/wp-content/plugins/` directory
3. Extract the zip file
4. Activate the plugin through the **Plugins** menu

## ⚙️ Configuration

### Initial Setup

1. Go to **WooCommerce > BDPay** in your admin panel
2. Configure your MFS wallet numbers
3. Enable desired payment methods
4. Set up payment instructions
5. Configure email notifications
6. Save settings

### Payment Gateway Setup

1. Go to **WooCommerce > Settings > Payments**
2. Find **BDPay - Manual Mobile Payment**
3. Click **Manage** to configure gateway settings
4. Enable the gateway and configure title/description
5. Save changes

## 🎯 How It Works

### For Customers

1. **Checkout**: Select BDPay at checkout and choose MFS method
2. **Payment**: Send money to displayed wallet number manually
3. **Submission**: Submit transaction ID and phone number
4. **Verification**: Wait for admin verification and order processing

### For Merchants

1. **Configuration**: Set up wallet numbers and instructions
2. **Monitoring**: Receive email notifications for new payments
3. **Verification**: Verify payments in WooCommerce admin
4. **Processing**: Process orders after payment verification

## 🛠️ Developer Guide

### Hooks and Filters

```php
// Payment submission hook
add_action( 'bdpay_payment_submitted', 'custom_payment_submitted', 10, 2 );

// Payment verification hook
add_action( 'bdpay_payment_verified', 'custom_payment_verified', 10, 2 );

// Modify payment methods
add_filter( 'bdpay_payment_methods', 'custom_payment_methods' );
```

### Template Override

Copy templates to your theme:

```
your-theme/
├── bdpay/
│   ├── checkout/
│   │   ├── payment-fields.php
│   │   ├── payment-form.php
│   │   └── payment-submitted.php
│   └── admin/
│       └── order-meta-box.php
```

### Custom Payment Methods

```php
function add_custom_payment_method( $methods ) {
    $methods['custom_mfs'] = __( 'Custom MFS', 'textdomain' );
    return $methods;
}
add_filter( 'bdpay_payment_methods', 'add_custom_payment_method' );
```

## 📊 Database Schema

### Transactions Table

```sql
CREATE TABLE wp_bdpay_transactions (
  id bigint(20) unsigned NOT NULL auto_increment,
  order_id bigint(20) unsigned NOT NULL,
  transaction_id varchar(100) NOT NULL,
  sender_phone varchar(20) NOT NULL,
  payment_method varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  status varchar(20) NOT NULL DEFAULT 'pending',
  screenshot_url varchar(255) DEFAULT NULL,
  notes text DEFAULT NULL,
  date_created datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  date_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);
```

## 🔍 Troubleshooting

### Common Issues

**Payment gateway not showing**
- Ensure WooCommerce is active
- Check if BDPay gateway is enabled
- Verify payment methods are configured

**Email notifications not working**
- Check email notification settings
- Verify WordPress mail configuration
- Test with SMTP plugin

**File upload issues**
- Check upload directory permissions
- Verify PHP upload limits
- Ensure allowed file types

### Debug Mode

Enable debug mode in wp-config.php:

```php
define( 'BDPAY_DEBUG', true );
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup

1. Clone the repository
2. Install dependencies
3. Set up local WordPress environment
4. Make your changes
5. Submit a pull request

### Coding Standards

- Follow WordPress Coding Standards
- Use PHP_CodeSniffer with WordPress rules
- Write comprehensive tests
- Document your code

## 📄 License

This project is licensed under the GPL v2 or later - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: [Full Documentation](DOCUMENTATION.md)
- **WordPress Support**: [Plugin Support Forum](https://wordpress.org/support/plugin/bdpay/)
- **GitHub Issues**: [Report Issues](https://github.com/bdpay/bdpay-wordpress/issues)
- **Email**: <EMAIL>

### Reporting Bugs

When reporting bugs, please include:
- WordPress version
- WooCommerce version
- PHP version
- Plugin version
- Error messages
- Steps to reproduce

## 🙏 Acknowledgments

- WordPress community for the excellent platform
- WooCommerce team for the e-commerce framework
- Bangladesh mobile financial service providers
- All contributors and users

## 📈 Roadmap

### Upcoming Features

- [ ] Automatic payment verification API
- [ ] Advanced reporting and analytics
- [ ] Multi-currency support
- [ ] Subscription payment support
- [ ] Mobile app integration
- [ ] Webhook support
- [ ] Advanced fraud detection

### Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Planned: API integration and advanced features
- **v1.2.0** - Planned: Analytics and reporting
- **v2.0.0** - Planned: Major feature additions

## 📞 Contact

- **Website**: https://bdpay.dev/
- **Email**: <EMAIL>
- **Support**: <EMAIL>
- **Security**: <EMAIL>

---

Made with ❤️ for the Bangladesh e-commerce community