<?php
/**
 * BDPay Security Class
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Security Class.
 */
class BDPay_Security {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init_security_measures' ) );
        add_filter( 'upload_mimes', array( $this, 'restrict_upload_mimes' ) );
        add_filter( 'wp_handle_upload_prefilter', array( $this, 'validate_file_upload' ) );
    }

    /**
     * Initialize security measures.
     */
    public function init_security_measures() {
        // Rate limiting for AJAX requests
        add_action( 'wp_ajax_bdpay_submit_payment', array( $this, 'check_rate_limit' ), 1 );
        add_action( 'wp_ajax_nopriv_bdpay_submit_payment', array( $this, 'check_rate_limit' ), 1 );
        
        // Additional security headers
        add_action( 'send_headers', array( $this, 'add_security_headers' ) );
    }

    /**
     * Check rate limit for payment submissions.
     */
    public function check_rate_limit() {
        $ip = bdpay_get_ip_address();
        $transient_key = 'bdpay_rate_limit_' . md5( $ip );
        $attempts = get_transient( $transient_key );

        if ( false === $attempts ) {
            $attempts = 0;
        }

        // Allow maximum 5 attempts per 15 minutes
        if ( $attempts >= 5 ) {
            wp_send_json_error( __( 'Too many attempts. Please try again later.', 'bdpay' ) );
        }

        // Increment attempts
        set_transient( $transient_key, $attempts + 1, 15 * MINUTE_IN_SECONDS );
    }

    /**
     * Restrict upload MIME types for BDPay uploads.
     *
     * @param array $mimes Allowed MIME types.
     * @return array
     */
    public function restrict_upload_mimes( $mimes ) {
        // Only allow image uploads for BDPay
        if ( $this->is_bdpay_upload() ) {
            return array(
                'jpg|jpeg|jpe' => 'image/jpeg',
                'png'          => 'image/png',
                'gif'          => 'image/gif',
                'webp'         => 'image/webp',
            );
        }

        return $mimes;
    }

    /**
     * Validate file uploads.
     *
     * @param array $file File data.
     * @return array
     */
    public function validate_file_upload( $file ) {
        if ( ! $this->is_bdpay_upload() ) {
            return $file;
        }

        // Check file size (max 5MB)
        if ( $file['size'] > 5 * 1024 * 1024 ) {
            $file['error'] = __( 'File size must be less than 5MB.', 'bdpay' );
            return $file;
        }

        // Check file type
        $allowed_types = array( 'image/jpeg', 'image/png', 'image/gif', 'image/webp' );
        if ( ! in_array( $file['type'], $allowed_types, true ) ) {
            $file['error'] = __( 'Only image files are allowed.', 'bdpay' );
            return $file;
        }

        // Additional security checks
        if ( ! $this->is_valid_image( $file['tmp_name'] ) ) {
            $file['error'] = __( 'Invalid image file.', 'bdpay' );
            return $file;
        }

        return $file;
    }

    /**
     * Check if current upload is for BDPay.
     *
     * @return bool
     */
    private function is_bdpay_upload() {
        return isset( $_POST['action'] ) && 'bdpay_submit_payment' === $_POST['action'];
    }

    /**
     * Validate if file is a valid image.
     *
     * @param string $file_path File path.
     * @return bool
     */
    private function is_valid_image( $file_path ) {
        if ( ! function_exists( 'getimagesize' ) ) {
            return true; // Skip validation if function not available
        }

        $image_info = getimagesize( $file_path );
        return false !== $image_info;
    }

    /**
     * Add security headers.
     */
    public function add_security_headers() {
        if ( ! headers_sent() && $this->is_bdpay_page() ) {
            header( 'X-Content-Type-Options: nosniff' );
            header( 'X-Frame-Options: SAMEORIGIN' );
            header( 'X-XSS-Protection: 1; mode=block' );
        }
    }

    /**
     * Check if current page is BDPay related.
     *
     * @return bool
     */
    private function is_bdpay_page() {
        return is_checkout() || is_account_page() || ( is_admin() && isset( $_GET['page'] ) && strpos( $_GET['page'], 'bdpay' ) !== false );
    }

    /**
     * Sanitize transaction ID.
     *
     * @param string $transaction_id Transaction ID.
     * @return string
     */
    public static function sanitize_transaction_id( $transaction_id ) {
        // Remove any non-alphanumeric characters
        $transaction_id = preg_replace( '/[^A-Za-z0-9]/', '', $transaction_id );
        
        // Limit length to 50 characters
        return substr( $transaction_id, 0, 50 );
    }

    /**
     * Sanitize phone number.
     *
     * @param string $phone Phone number.
     * @return string
     */
    public static function sanitize_phone_number( $phone ) {
        // Remove any non-digit characters
        $phone = preg_replace( '/[^0-9]/', '', $phone );
        
        // Limit length to 15 characters
        return substr( $phone, 0, 15 );
    }

    /**
     * Validate transaction ID format.
     *
     * @param string $transaction_id Transaction ID.
     * @return bool
     */
    public static function validate_transaction_id( $transaction_id ) {
        // Must be alphanumeric and between 8-50 characters
        return preg_match( '/^[A-Za-z0-9]{8,50}$/', $transaction_id );
    }

    /**
     * Validate phone number format.
     *
     * @param string $phone Phone number.
     * @return bool
     */
    public static function validate_phone_number( $phone ) {
        return bdpay_validate_phone( $phone );
    }

    /**
     * Check for duplicate transaction ID.
     *
     * @param string $transaction_id Transaction ID.
     * @param int    $exclude_order_id Order ID to exclude from check.
     * @return bool
     */
    public static function is_duplicate_transaction_id( $transaction_id, $exclude_order_id = 0 ) {
        global $wpdb;

        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}bdpay_transactions WHERE transaction_id = %s",
            $transaction_id
        );

        if ( $exclude_order_id > 0 ) {
            $query .= $wpdb->prepare( " AND order_id != %d", $exclude_order_id );
        }

        return (int) $wpdb->get_var( $query ) > 0;
    }

    /**
     * Generate secure nonce for BDPay actions.
     *
     * @param string $action Action name.
     * @return string
     */
    public static function create_nonce( $action ) {
        return wp_create_nonce( 'bdpay_' . $action );
    }

    /**
     * Verify BDPay nonce.
     *
     * @param string $nonce Nonce value.
     * @param string $action Action name.
     * @return bool
     */
    public static function verify_nonce( $nonce, $action ) {
        return wp_verify_nonce( $nonce, 'bdpay_' . $action );
    }

    /**
     * Check if user has permission for BDPay actions.
     *
     * @param string $capability Required capability.
     * @return bool
     */
    public static function check_permission( $capability = 'manage_woocommerce' ) {
        return current_user_can( $capability );
    }

    /**
     * Log security event.
     *
     * @param string $event Event description.
     * @param array  $data Additional data.
     */
    public static function log_security_event( $event, $data = array() ) {
        $log_data = array(
            'event'     => $event,
            'ip'        => bdpay_get_ip_address(),
            'user_id'   => get_current_user_id(),
            'timestamp' => current_time( 'mysql' ),
            'data'      => $data,
        );

        bdpay_log( 'Security Event: ' . wp_json_encode( $log_data ), 'warning', 'bdpay-security' );
    }

    /**
     * Sanitize and validate order data.
     *
     * @param array $data Order data.
     * @return array|WP_Error
     */
    public static function validate_order_data( $data ) {
        $errors = new WP_Error();

        // Validate order ID
        if ( empty( $data['order_id'] ) || ! is_numeric( $data['order_id'] ) ) {
            $errors->add( 'invalid_order_id', __( 'Invalid order ID.', 'bdpay' ) );
        }

        // Validate payment method
        if ( empty( $data['payment_method'] ) ) {
            $errors->add( 'missing_payment_method', __( 'Payment method is required.', 'bdpay' ) );
        } else {
            $enabled_methods = BDPay_Core::get_enabled_payment_methods();
            if ( ! array_key_exists( $data['payment_method'], $enabled_methods ) ) {
                $errors->add( 'invalid_payment_method', __( 'Invalid payment method.', 'bdpay' ) );
            }
        }

        // Validate transaction ID
        if ( empty( $data['transaction_id'] ) ) {
            $errors->add( 'missing_transaction_id', __( 'Transaction ID is required.', 'bdpay' ) );
        } else {
            $data['transaction_id'] = self::sanitize_transaction_id( $data['transaction_id'] );
            if ( ! self::validate_transaction_id( $data['transaction_id'] ) ) {
                $errors->add( 'invalid_transaction_id', __( 'Invalid transaction ID format.', 'bdpay' ) );
            }
        }

        // Validate phone number
        if ( empty( $data['sender_phone'] ) ) {
            $errors->add( 'missing_phone', __( 'Sender phone number is required.', 'bdpay' ) );
        } else {
            $data['sender_phone'] = self::sanitize_phone_number( $data['sender_phone'] );
            if ( ! self::validate_phone_number( $data['sender_phone'] ) ) {
                $errors->add( 'invalid_phone', __( 'Invalid phone number format.', 'bdpay' ) );
            }
        }

        // Check for duplicate transaction ID
        if ( ! empty( $data['transaction_id'] ) && self::is_duplicate_transaction_id( $data['transaction_id'], $data['order_id'] ) ) {
            $errors->add( 'duplicate_transaction_id', __( 'This transaction ID has already been used.', 'bdpay' ) );
        }

        if ( $errors->has_errors() ) {
            return $errors;
        }

        return $data;
    }

    /**
     * Clean and validate notes field.
     *
     * @param string $notes Notes text.
     * @return string
     */
    public static function sanitize_notes( $notes ) {
        // Remove any HTML tags and limit length
        $notes = wp_strip_all_tags( $notes );
        return substr( $notes, 0, 500 );
    }
}

new BDPay_Security();
