<?php
/**
 * Installation related functions and actions.
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Install Class.
 */
class BDPay_Install {

    /**
     * DB updates and callbacks that need to be run per version.
     *
     * @var array
     */
    private static $db_updates = array(
        '1.0.0' => array(
            'bdpay_100_create_tables',
            'bdpay_100_update_db_version',
        ),
    );

    /**
     * Hook in tabs.
     */
    public static function init() {
        add_action( 'init', array( __CLASS__, 'check_version' ), 5 );
        add_action( 'admin_init', array( __CLASS__, 'install_actions' ) );
    }

    /**
     * Check BDPay version and run the updater if necessary.
     *
     * This check is done on all requests and runs if the versions do not match.
     */
    public static function check_version() {
        if ( ! defined( 'IFRAME_REQUEST' ) && version_compare( get_option( 'bdpay_version' ), BDPAY_VERSION, '<' ) ) {
            self::install();
            do_action( 'bdpay_updated' );
        }
    }

    /**
     * Install actions when a update button is clicked within the admin area.
     *
     * This function is hooked into admin_init to affect admin only.
     */
    public static function install_actions() {
        if ( ! empty( $_GET['do_update_bdpay'] ) ) { // WPCS: input var ok.
            check_admin_referer( 'bdpay_db_update', 'bdpay_db_update_nonce' );
            self::update();
            wp_safe_redirect( admin_url( 'admin.php?page=bdpay-settings' ) );
            exit;
        }
    }

    /**
     * Install BDPay.
     */
    public static function install() {
        if ( ! is_blog_installed() ) {
            return;
        }

        // Check if we are not already running this routine.
        if ( 'yes' === get_transient( 'bdpay_installing' ) ) {
            return;
        }

        // If we made it till here nothing is running yet, lets set the transient now.
        set_transient( 'bdpay_installing', 'yes', MINUTE_IN_SECONDS * 10 );

        self::create_options();
        self::create_tables();
        self::update_bdpay_version();
        self::maybe_update_db_version();

        delete_transient( 'bdpay_installing' );

        do_action( 'bdpay_installed' );
    }

    /**
     * Update BDPay version to current.
     */
    private static function update_bdpay_version() {
        delete_option( 'bdpay_version' );
        add_option( 'bdpay_version', BDPAY_VERSION );
    }

    /**
     * Get list of DB update callbacks.
     *
     * @since  1.0.0
     * @return array
     */
    public static function get_db_update_callbacks() {
        return self::$db_updates;
    }

    /**
     * Push all needed DB updates to the queue for processing.
     */
    private static function update() {
        $current_db_version = get_option( 'bdpay_db_version' );
        $loop               = 0;

        foreach ( self::get_db_update_callbacks() as $version => $update_callbacks ) {
            if ( version_compare( $current_db_version, $version, '<' ) ) {
                foreach ( $update_callbacks as $update_callback ) {
                    $loop++;
                }
            }
        }
    }

    /**
     * Update DB version to current.
     *
     * @param string|null $version New BDPay DB version or null.
     */
    public static function update_db_version( $version = null ) {
        delete_option( 'bdpay_db_version' );
        add_option( 'bdpay_db_version', is_null( $version ) ? BDPAY_VERSION : $version );
    }

    /**
     * Maybe update DB version to current.
     */
    private static function maybe_update_db_version() {
        if ( get_option( 'bdpay_db_version' ) !== BDPAY_VERSION ) {
            self::update_db_version();
        }
    }

    /**
     * Default options.
     *
     * Sets up the default options used on the settings pages.
     */
    private static function create_options() {
        // Set up default options for BDPay
        $default_options = array(
            // General settings
            'bdpay_enable_screenshot' => 'yes',
            'bdpay_enable_notifications' => 'yes',

            // Payment method settings - enable bKash by default as an example
            'bdpay_enable_bkash' => 'yes',
            'bdpay_wallet_bkash' => '',
            'bdpay_instructions_bkash' => __( 'Send money to the bKash number provided and submit your transaction details.', 'bdpay' ),

            'bdpay_enable_nagad' => 'no',
            'bdpay_wallet_nagad' => '',
            'bdpay_instructions_nagad' => __( 'Send money to the Nagad number provided and submit your transaction details.', 'bdpay' ),

            'bdpay_enable_rocket' => 'no',
            'bdpay_wallet_rocket' => '',
            'bdpay_instructions_rocket' => __( 'Send money to the Rocket number provided and submit your transaction details.', 'bdpay' ),

            'bdpay_enable_upay' => 'no',
            'bdpay_wallet_upay' => '',
            'bdpay_instructions_upay' => __( 'Send money to the Upay number provided and submit your transaction details.', 'bdpay' ),

            'bdpay_enable_surecash' => 'no',
            'bdpay_wallet_surecash' => '',
            'bdpay_instructions_surecash' => __( 'Send money to the SureCash number provided and submit your transaction details.', 'bdpay' ),
        );

        foreach ( $default_options as $option_name => $default_value ) {
            add_option( $option_name, $default_value );
        }
    }

    /**
     * Set up the database tables which the plugin needs to function.
     */
    private static function create_tables() {
        global $wpdb;

        $wpdb->hide_errors();

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';

        dbDelta( self::get_schema() );
    }

    /**
     * Get Table schema.
     *
     * @return string
     */
    private static function get_schema() {
        global $wpdb;

        $collate = '';

        if ( $wpdb->has_cap( 'collation' ) ) {
            $collate = $wpdb->get_charset_collate();
        }

        $tables = "
CREATE TABLE {$wpdb->prefix}bdpay_transactions (
  id bigint(20) unsigned NOT NULL auto_increment,
  order_id bigint(20) unsigned NOT NULL,
  transaction_id varchar(100) NOT NULL,
  sender_phone varchar(20) NOT NULL,
  payment_method varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  status varchar(20) NOT NULL DEFAULT 'pending',
  screenshot_url varchar(255) DEFAULT NULL,
  notes text DEFAULT NULL,
  date_created datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  date_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY order_id (order_id),
  KEY transaction_id (transaction_id),
  KEY status (status),
  KEY date_created (date_created)
) $collate;
";

        return $tables;
    }

    /**
     * Uninstall tables when MU blog is deleted.
     *
     * @param array $tables List of tables that will be deleted by WP.
     *
     * @return array
     */
    public static function wpmu_drop_tables( $tables ) {
        global $wpdb;

        $tables[] = $wpdb->prefix . 'bdpay_transactions';

        return $tables;
    }

    /**
     * Deactivation hook.
     */
    public static function deactivate() {
        wp_clear_scheduled_hook( 'bdpay_cleanup_sessions' );
    }
}

BDPay_Install::init();
