<?php
/**
 * BDPay Core Functions
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * Clean variables using sanitize_text_field. Arrays are cleaned recursively.
 * Non-scalar values are ignored.
 *
 * @param string|array $var Data to sanitize.
 * @return string|array
 */
function bdpay_clean( $var ) {
    if ( is_array( $var ) ) {
        return array_map( 'bdpay_clean', $var );
    } else {
        return is_scalar( $var ) ? sanitize_text_field( $var ) : $var;
    }
}

/**
 * Sanitize textarea field.
 *
 * @param string $str String to sanitize.
 * @return string
 */
function bdpay_sanitize_textarea( $str ) {
    return sanitize_textarea_field( $str );
}

/**
 * Get BDPay logger instance.
 *
 * @return WC_Logger
 */
function bdpay_get_logger() {
    static $logger;
    
    if ( null === $logger ) {
        $logger = wc_get_logger();
    }
    
    return $logger;
}

/**
 * Log a message.
 *
 * @param string $message Log message.
 * @param string $level Log level (emergency|alert|critical|error|warning|notice|info|debug).
 * @param string $source Log source.
 */
function bdpay_log( $message, $level = 'info', $source = 'bdpay' ) {
    if ( function_exists( 'wc_get_logger' ) ) {
        $logger = bdpay_get_logger();
        $logger->log( $level, $message, array( 'source' => $source ) );
    }
}

/**
 * Display a help tip.
 *
 * @param string $tip Help tip text.
 * @param bool   $allow_html Allow HTML in tip.
 * @return string
 */
function bdpay_help_tip( $tip, $allow_html = false ) {
    if ( $allow_html ) {
        $tip = bdpay_sanitize_tooltip( $tip );
    } else {
        $tip = esc_attr( $tip );
    }

    return '<span class="bdpay-help-tip" data-tip="' . $tip . '"></span>';
}

/**
 * Sanitize a string destined to be a tooltip.
 *
 * @param string $var Data to sanitize.
 * @return string
 */
function bdpay_sanitize_tooltip( $var ) {
    return htmlspecialchars(
        wp_kses(
            html_entity_decode( $var ),
            array(
                'br'     => array(),
                'em'     => array(),
                'strong' => array(),
                'small'  => array(),
                'span'   => array(),
                'ul'     => array(),
                'li'     => array(),
                'ol'     => array(),
                'p'      => array(),
            )
        )
    );
}

/**
 * Get BDPay template.
 *
 * @param string $template_name Template name.
 * @param array  $args Template arguments.
 * @param string $template_path Template path.
 * @param string $default_path Default path.
 */
function bdpay_get_template( $template_name, $args = array(), $template_path = '', $default_path = '' ) {
    $cache_key = sanitize_key( implode( '-', array( 'template', $template_name, $template_path, $default_path, BDPAY_VERSION ) ) );
    $template  = (string) wp_cache_get( $cache_key, 'bdpay' );

    if ( ! $template ) {
        $template = bdpay_locate_template( $template_name, $template_path, $default_path );
        wp_cache_set( $cache_key, $template, 'bdpay' );
    }

    // Allow 3rd party plugins to filter template file from their plugin.
    $filter_template = apply_filters( 'bdpay_get_template', $template, $template_name, $args, $template_path, $default_path );

    if ( $filter_template !== $template ) {
        if ( ! file_exists( $filter_template ) ) {
            /* translators: %s template */
            bdpay_doing_it_wrong( __FUNCTION__, sprintf( __( '%s does not exist.', 'bdpay' ), '<code>' . $template . '</code>' ), '1.0.0' );
            return;
        }
        $template = $filter_template;
    }

    $action_args = array(
        'template_name' => $template_name,
        'template_path' => $template_path,
        'located'       => $template,
        'args'          => $args,
    );

    if ( ! empty( $args ) && is_array( $args ) ) {
        if ( isset( $args['action_args'] ) ) {
            bdpay_doing_it_wrong(
                __FUNCTION__,
                __( 'action_args should not be overwritten when calling bdpay_get_template.', 'bdpay' ),
                '1.0.0'
            );
            unset( $args['action_args'] );
        }
        extract( $args ); // @codingStandardsIgnoreLine
    }

    do_action( 'bdpay_before_template_part', $action_args['template_name'], $action_args['template_path'], $action_args['located'], $action_args['args'] );

    include $action_args['located'];

    do_action( 'bdpay_after_template_part', $action_args['template_name'], $action_args['template_path'], $action_args['located'], $action_args['args'] );
}

/**
 * Locate a template and return the path for inclusion.
 *
 * @param string $template_name Template name.
 * @param string $template_path Template path.
 * @param string $default_path Default path.
 * @return string
 */
function bdpay_locate_template( $template_name, $template_path = '', $default_path = '' ) {
    if ( ! $template_path ) {
        $template_path = bdpay()->template_path();
    }

    if ( ! $default_path ) {
        $default_path = BDPAY_PLUGIN_PATH . '/templates/';
    }

    // Look within passed path within the theme - this is priority.
    $template = locate_template(
        array(
            trailingslashit( $template_path ) . $template_name,
            $template_name,
        )
    );

    // Get default template/
    if ( ! $template || BDPAY_TEMPLATE_DEBUG_MODE ) {
        $template = $default_path . $template_name;
    }

    // Return what we found.
    return apply_filters( 'bdpay_locate_template', $template, $template_name, $template_path );
}

/**
 * Get template part (for templates like the shop-loop).
 *
 * @param string $slug Template slug.
 * @param string $name Template name (default: '').
 */
function bdpay_get_template_part( $slug, $name = '' ) {
    $cache_key = sanitize_key( implode( '-', array( 'template-part', $slug, $name, BDPAY_VERSION ) ) );
    $template  = (string) wp_cache_get( $cache_key, 'bdpay' );

    if ( ! $template ) {
        if ( $name ) {
            $template = BDPAY_TEMPLATE_DEBUG_MODE ? '' : locate_template(
                array(
                    "{$slug}-{$name}.php",
                    bdpay()->template_path() . "{$slug}-{$name}.php",
                )
            );

            if ( ! $template ) {
                $fallback = BDPAY_PLUGIN_PATH . "/templates/{$slug}-{$name}.php";
                $template = file_exists( $fallback ) ? $fallback : '';
            }
        }

        if ( ! $template ) {
            // If template file doesn't exist, look in yourtheme/slug.php and yourtheme/bdpay/slug.php.
            $template = BDPAY_TEMPLATE_DEBUG_MODE ? '' : locate_template(
                array(
                    "{$slug}.php",
                    bdpay()->template_path() . "{$slug}.php",
                )
            );
        }

        wp_cache_set( $cache_key, $template, 'bdpay' );
    }

    if ( $template ) {
        load_template( $template, false );
    }
}

/**
 * Get other templates (e.g. product attributes) passing attributes and including the file.
 *
 * @param string $template_name Template name.
 * @param array  $args Template arguments.
 * @param string $template_path Template path.
 * @param string $default_path Default path.
 */
function bdpay_get_template_html( $template_name, $args = array(), $template_path = '', $default_path = '' ) {
    ob_start();
    bdpay_get_template( $template_name, $args, $template_path, $default_path );
    return ob_get_clean();
}

/**
 * Wrapper for bdpay_doing_it_wrong.
 *
 * @param string $function Function used.
 * @param string $message Message to log.
 * @param string $version Version the message was added in.
 */
function bdpay_doing_it_wrong( $function, $message, $version ) {
    // @codingStandardsIgnoreStart
    $message .= ' Backtrace: ' . wp_debug_backtrace_summary();

    if ( wp_doing_ajax() || bdpay_is_rest_api_request() ) {
        do_action( 'doing_it_wrong_run', $function, $message, $version );
        error_log( "{$function} was called incorrectly. {$message}. This message was added in version {$version}." );
    } else {
        _doing_it_wrong( $function, $message, $version );
    }
    // @codingStandardsIgnoreEnd
}

/**
 * Returns true if the request is a non-legacy REST API request.
 *
 * @return bool
 */
function bdpay_is_rest_api_request() {
    if ( empty( $_SERVER['REQUEST_URI'] ) ) {
        return false;
    }

    $rest_prefix         = trailingslashit( rest_get_url_prefix() );
    $is_rest_api_request = ( false !== strpos( $_SERVER['REQUEST_URI'], $rest_prefix ) ); // phpcs:disable WordPress.Security.ValidatedSanitizedInput.MissingUnslash, WordPress.Security.ValidatedSanitizedInput.InputNotSanitized

    return apply_filters( 'bdpay_is_rest_api_request', $is_rest_api_request );
}

/**
 * Format phone number for Bangladesh.
 *
 * @param string $phone Phone number.
 * @return string
 */
function bdpay_format_phone( $phone ) {
    // Remove any non-digit characters
    $phone = preg_replace( '/[^0-9]/', '', $phone );
    
    // Format Bangladesh mobile numbers
    if ( preg_match( '/^(01[3-9]\d{8})$/', $phone ) ) {
        return substr( $phone, 0, 3 ) . '-' . substr( $phone, 3, 4 ) . '-' . substr( $phone, 7 );
    } elseif ( preg_match( '/^(8801[3-9]\d{8})$/', $phone ) ) {
        return '+88-' . substr( $phone, 2, 3 ) . '-' . substr( $phone, 5, 4 ) . '-' . substr( $phone, 9 );
    }
    
    return $phone;
}

/**
 * Validate Bangladesh phone number.
 *
 * @param string $phone Phone number.
 * @return bool
 */
function bdpay_validate_phone( $phone ) {
    // Remove any non-digit characters
    $phone = preg_replace( '/[^0-9]/', '', $phone );
    
    // Check if it's a valid Bangladesh mobile number
    return preg_match( '/^(01[3-9]\d{8}|8801[3-9]\d{8})$/', $phone );
}

/**
 * Get formatted price.
 *
 * @param float $price Price.
 * @return string
 */
function bdpay_price( $price ) {
    if ( function_exists( 'wc_price' ) ) {
        return wc_price( $price );
    }
    
    return '৳' . number_format( $price, 2 );
}

/**
 * Get current user IP address.
 *
 * @return string
 */
function bdpay_get_ip_address() {
    if ( isset( $_SERVER['HTTP_X_REAL_IP'] ) ) {
        return sanitize_text_field( wp_unslash( $_SERVER['HTTP_X_REAL_IP'] ) );
    } elseif ( isset( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
        // Proxy servers can send through this header like this: X-Forwarded-For: client1, proxy1, proxy2
        // Make sure we always only send through the first IP in the list which should always be the client IP.
        return (string) rest_is_ip_address( trim( current( preg_split( '/,/', sanitize_text_field( wp_unslash( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) ) ) ) );
    } elseif ( isset( $_SERVER['REMOTE_ADDR'] ) ) {
        return sanitize_text_field( wp_unslash( $_SERVER['REMOTE_ADDR'] ) );
    }
    return '';
}

/**
 * Check if current user can manage BDPay.
 *
 * @return bool
 */
function bdpay_current_user_can_manage() {
    return current_user_can( 'manage_woocommerce' ) || current_user_can( 'manage_options' );
}

/**
 * Get BDPay version.
 *
 * @return string
 */
function bdpay_get_version() {
    return BDPAY_VERSION;
}

/**
 * Check if WooCommerce is active.
 *
 * @return bool
 */
function bdpay_is_woocommerce_active() {
    return class_exists( 'WooCommerce' );
}

/**
 * Get payment method icon URL.
 *
 * @param string $method Payment method.
 * @return string
 */
function bdpay_get_method_icon( $method ) {
    $icon_file = BDPAY_PLUGIN_PATH . 'assets/images/' . $method . '.png';
    
    if ( file_exists( $icon_file ) ) {
        return BDPAY_ASSETS_URL . 'images/' . $method . '.png';
    }
    
    return BDPAY_ASSETS_URL . 'images/default-method.png';
}
