<?php
/**
 * BDPay Payment Gateway
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

// Check if WooCommerce is active and WC_Payment_Gateway class exists
if ( ! class_exists( 'WC_Payment_Gateway' ) ) {
    return;
}

/**
 * BDPay_Gateway Class.
 */
class BDPay_Gateway extends WC_Payment_Gateway {

    /**
     * Constructor for the gateway.
     */
    public function __construct() {
        $this->id                 = 'bdpay';
        // $this->icon               = apply_filters( 'bdpay_gateway_icon', BDPAY_ASSETS_URL . 'images/bdpay-icon.png' );
        $this->has_fields         = true;
        $this->method_title       = esc_html__( 'BDPay - Manual Mobile Payment', 'bdpay' );
        $this->method_description = esc_html__( 'Accept manual payments via Bangladesh mobile financial services (bKash, Nagad, Rocket, etc.)', 'bdpay' );
        $this->supports           = array(
            'products',
            'refunds'
        );

        // Load the settings.
        $this->init_form_fields();
        $this->init_settings();

        // Define user set variables
        $this->title        = $this->get_option( 'title' );
        $this->description  = $this->get_option( 'description' );
        $this->instructions = $this->get_option( 'instructions', $this->description );

        // Actions
        add_action( 'woocommerce_update_options_payment_gateways_' . $this->id, array( $this, 'process_admin_options' ) );
        add_action( 'woocommerce_thankyou_' . $this->id, array( $this, 'thankyou_page' ) );
        add_action( 'woocommerce_email_before_order_table', array( $this, 'email_instructions' ), 10, 3 );
        add_action( 'wp_ajax_bdpay_submit_payment', array( $this, 'submit_payment_ajax' ) );
        add_action( 'wp_ajax_nopriv_bdpay_submit_payment', array( $this, 'submit_payment_ajax' ) );
    }

    /**
     * Initialize Gateway Settings Form Fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __( 'Enable/Disable', 'bdpay' ),
                'type'    => 'checkbox',
                'label'   => __( 'Enable BDPay Payment', 'bdpay' ),
                'default' => 'no'
            ),
            'title' => array(
                'title'       => __( 'Title', 'bdpay' ),
                'type'        => 'text',
                'description' => __( 'This controls the title for the payment method the customer sees during checkout.', 'bdpay' ),
                'default'     => __( 'Mobile Payment (bKash, Nagad, Rocket, etc.)', 'bdpay' ),
                'desc_tip'    => true,
            ),
            'description' => array(
                'title'       => __( 'Description', 'bdpay' ),
                'type'        => 'textarea',
                'description' => __( 'Payment method description that the customer will see on your checkout.', 'bdpay' ),
                'default'     => __( 'Pay using your mobile wallet. You will need to send money manually and provide transaction details.', 'bdpay' ),
                'desc_tip'    => true,
            ),
            'instructions' => array(
                'title'       => __( 'Instructions', 'bdpay' ),
                'type'        => 'textarea',
                'description' => __( 'Instructions that will be added to the thank you page and emails.', 'bdpay' ),
                'default'     => __( 'Please send the exact amount to the provided mobile wallet number and submit your transaction details below.', 'bdpay' ),
                'desc_tip'    => true,
            ),
        );
    }

    /**
     * Output for the order received page.
     */
    public function thankyou_page( $order_id ) {
        if ( $this->instructions ) {
            echo wp_kses_post( wpautop( wptexturize( $this->instructions ) ) );
        }
        $this->payment_details( $order_id );
    }

    /**
     * Add content to the WC emails.
     *
     * @param WC_Order $order
     * @param bool     $sent_to_admin
     * @param bool     $plain_text
     */
    public function email_instructions( $order, $sent_to_admin, $plain_text = false ) {
        if ( $this->instructions && ! $sent_to_admin && $this->id === $order->get_payment_method() && $order->has_status( 'on-hold' ) ) {
            echo wp_kses_post( wpautop( wptexturize( $this->instructions ) ) ) . PHP_EOL;
        }
    }

    /**
     * Payment form on checkout page
     */
    public function payment_fields() {
        if ( $this->description ) {
            echo wpautop( wptexturize( $this->description ) );
        }

        $enabled_methods = BDPay_Core::get_enabled_payment_methods();
        
        if ( empty( $enabled_methods ) ) {
            echo '<p class="bdpay-error">' . esc_html__( 'No payment methods are currently available. Please contact the store administrator.', 'bdpay' ) . '</p>';
            return;
        }

        include BDPAY_TEMPLATES_PATH . 'checkout/payment-fields.php';
    }

    /**
     * Process the payment and return the result
     *
     * @param int $order_id
     * @return array
     */
    public function process_payment( $order_id ) {
        $order = wc_get_order( $order_id );

        // Get payment data from checkout form
        $payment_method = sanitize_text_field( $_POST['bdpay_payment_method'] ?? '' );
        $transaction_id = sanitize_text_field( $_POST['bdpay_transaction_id'] ?? '' );
        $sender_phone = sanitize_text_field( $_POST['bdpay_sender_phone'] ?? '' );
        $notes = sanitize_textarea_field( $_POST['bdpay_notes'] ?? '' );

        // Validate required fields
        if ( empty( $payment_method ) || empty( $transaction_id ) || empty( $sender_phone ) ) {
            wc_add_notice( __( 'Please fill in all required payment fields.', 'bdpay' ), 'error' );
            return array( 'result' => 'failure' );
        }

        // Validate phone number
        if ( ! $this->validate_phone_number( $sender_phone ) ) {
            wc_add_notice( __( 'Please enter a valid Bangladesh phone number.', 'bdpay' ), 'error' );
            return array( 'result' => 'failure' );
        }

        // Handle screenshot upload (optional - temporarily disabled)
        $screenshot_url = '';
        if ( ! empty( $_FILES['bdpay_screenshot'] ) && $_FILES['bdpay_screenshot']['error'] === UPLOAD_ERR_OK ) {
            $screenshot_url = $this->handle_screenshot_upload( $_FILES['bdpay_screenshot'] );
            if ( is_wp_error( $screenshot_url ) ) {
                // Log error but don't fail the payment
                error_log( 'BDPay screenshot upload failed: ' . $screenshot_url->get_error_message() );
                $screenshot_url = '';
            }
        }

        // Create transaction record with submitted data
        $transaction = new BDPay_Transaction();
        $transaction_data = array(
            'order_id'       => $order_id,
            'transaction_id' => $transaction_id,
            'sender_phone'   => $sender_phone,
            'payment_method' => $payment_method,
            'amount'         => $order->get_total(),
            'status'         => 'submitted',
            'screenshot_url' => $screenshot_url,
            'notes'          => $notes,
        );
        $transaction->create( $transaction_data );

        // Mark as verifying (payment submitted, awaiting verification)
        $order->update_status( 'bdpay-verifying', __( 'BDPay payment submitted, awaiting verification.', 'bdpay' ) );

        // Add order note
        $order->add_order_note( sprintf(
            __( 'BDPay payment submitted. Method: %1$s, Transaction ID: %2$s, Phone: %3$s', 'bdpay' ),
            $payment_method,
            $transaction_id,
            $sender_phone
        ) );

        // Send email notifications
        do_action( 'bdpay_payment_submitted', $order, $transaction );

        // Reduce stock levels
        wc_reduce_stock_levels( $order_id );

        // Remove cart
        WC()->cart->empty_cart();

        // Return thankyou redirect
        return array(
            'result'   => 'success',
            'redirect' => $this->get_return_url( $order )
        );
    }

    /**
     * Output payment details on thank you page
     *
     * @param int $order_id
     */
    public function payment_details( $order_id ) {
        $order = wc_get_order( $order_id );
        
        if ( ! $order || $this->id !== $order->get_payment_method() ) {
            return;
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order_id );
        
        if ( $transaction && 'pending' !== $transaction->get_status() ) {
            // Payment already submitted
            include BDPAY_TEMPLATES_PATH . 'checkout/payment-submitted.php';
        } else {
            // Show payment form
            $enabled_methods = BDPay_Core::get_enabled_payment_methods();
            include BDPAY_TEMPLATES_PATH . 'checkout/payment-form.php';
        }
    }

    /**
     * Handle payment submission via AJAX
     */
    public function submit_payment_ajax() {
        check_ajax_referer( 'bdpay_frontend', 'nonce' );

        $order_id = intval( $_POST['order_id'] );
        $payment_method = sanitize_text_field( $_POST['payment_method'] );
        $transaction_id = sanitize_text_field( $_POST['transaction_id'] );
        $sender_phone = sanitize_text_field( $_POST['sender_phone'] );
        $notes = sanitize_textarea_field( $_POST['notes'] );

        // Validate inputs
        if ( empty( $order_id ) || empty( $payment_method ) || empty( $transaction_id ) || empty( $sender_phone ) ) {
            wp_send_json_error( __( 'All required fields must be filled.', 'bdpay' ) );
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( __( 'Invalid order.', 'bdpay' ) );
        }

        // Check if payment already submitted
        $existing_transaction = BDPay_Transaction::get_by_order_id( $order_id );
        if ( $existing_transaction && 'pending' !== $existing_transaction->get_status() ) {
            wp_send_json_error( __( 'Payment has already been submitted for this order.', 'bdpay' ) );
        }

        // Validate phone number
        if ( ! $this->validate_phone_number( $sender_phone ) ) {
            wp_send_json_error( __( 'Please enter a valid Bangladesh phone number.', 'bdpay' ) );
        }

        // Handle screenshot upload (optional)
        $screenshot_url = '';
        if ( ! empty( $_FILES['screenshot'] ) && $_FILES['screenshot']['error'] === UPLOAD_ERR_OK ) {
            $screenshot_url = $this->handle_screenshot_upload( $_FILES['screenshot'] );
            if ( is_wp_error( $screenshot_url ) ) {
                // Log error but don't fail the submission
                error_log( 'BDPay screenshot upload failed: ' . $screenshot_url->get_error_message() );
                $screenshot_url = '';
            }
        }

        // Create or update transaction
        $transaction_data = array(
            'order_id'       => $order_id,
            'transaction_id' => $transaction_id,
            'sender_phone'   => $sender_phone,
            'payment_method' => $payment_method,
            'amount'         => $order->get_total(),
            'status'         => 'submitted',
            'screenshot_url' => $screenshot_url,
            'notes'          => $notes,
        );

        if ( $existing_transaction ) {
            $transaction = $existing_transaction;
            $transaction->update( $transaction_data );
        } else {
            $transaction = new BDPay_Transaction();
            $transaction->create( $transaction_data );
        }

        // Update order status
        $order->update_status( 'bdpay-verifying', __( 'BDPay payment submitted, awaiting verification.', 'bdpay' ) );

        // Add order note
        $order->add_order_note( sprintf(
            __( 'BDPay payment submitted. Method: %1$s, Transaction ID: %2$s, Phone: %3$s', 'bdpay' ),
            $payment_method,
            $transaction_id,
            $sender_phone
        ) );

        // Send email notifications
        do_action( 'bdpay_payment_submitted', $order, $transaction );

        wp_send_json_success( array(
            'message' => __( 'Payment details submitted successfully. We will verify your payment and update your order status.', 'bdpay' ),
            'redirect' => $order->get_view_order_url()
        ) );
    }

    /**
     * Validate Bangladesh phone number
     *
     * @param string $phone
     * @return bool
     */
    private function validate_phone_number( $phone ) {
        // Remove any non-digit characters
        $phone = preg_replace( '/[^0-9]/', '', $phone );
        
        // Check if it's a valid Bangladesh mobile number
        // Bangladesh mobile numbers: 01XXXXXXXXX (11 digits) or 8801XXXXXXXXX (13 digits)
        if ( preg_match( '/^(01[3-9]\d{8}|8801[3-9]\d{8})$/', $phone ) ) {
            return true;
        }
        
        return false;
    }

    /**
     * Handle screenshot upload
     *
     * @param array $file
     * @return string|WP_Error
     */
    private function handle_screenshot_upload( $file ) {
        if ( ! function_exists( 'wp_handle_upload' ) ) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        $upload_overrides = array(
            'test_form' => false,
            'mimes'     => array(
                'jpg|jpeg|jpe' => 'image/jpeg',
                'png'          => 'image/png',
                'gif'          => 'image/gif',
            ),
        );

        $uploaded_file = wp_handle_upload( $file, $upload_overrides );

        if ( isset( $uploaded_file['error'] ) ) {
            return new WP_Error( 'upload_error', $uploaded_file['error'] );
        }

        return $uploaded_file['url'];
    }

    /**
     * Process refund.
     *
     * @param int    $order_id Order ID.
     * @param float  $amount Refund amount.
     * @param string $reason Refund reason.
     * @return boolean True or false based on success, or a WP_Error object.
     */
    public function process_refund( $order_id, $amount = null, $reason = '' ) {
        $order = wc_get_order( $order_id );

        if ( ! $order ) {
            return false;
        }

        // For manual payment gateways, we just mark the refund as processed
        // The actual refund needs to be handled manually by the store owner
        $order->add_order_note(
            sprintf(
                __( 'Refund of %1$s requested via BDPay. Reason: %2$s. Please process this refund manually through your mobile banking service.', 'bdpay' ),
                wc_price( $amount ),
                $reason
            )
        );

        return true;
    }
}

// Ensure gateway is registered with WooCommerce
add_action( 'woocommerce_loaded', 'bdpay_register_gateway' );
add_action( 'init', 'bdpay_register_gateway', 20 );

function bdpay_register_gateway() {
    if ( class_exists( 'WooCommerce' ) && ! has_filter( 'woocommerce_payment_gateways', 'bdpay_add_gateway_to_wc' ) ) {
        add_filter( 'woocommerce_payment_gateways', 'bdpay_add_gateway_to_wc' );
    }
}

function bdpay_add_gateway_to_wc( $gateways ) {
    if ( class_exists( 'BDPay_Gateway' ) ) {
        $gateways[] = 'BDPay_Gateway';
    }
    return $gateways;
}
