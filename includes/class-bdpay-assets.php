<?php
/**
 * BDPay Assets
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Assets class.
 */
class BDPay_Assets {

    /**
     * Hook in methods.
     */
    public static function init() {
        add_action( 'wp_enqueue_scripts', array( __CLASS__, 'load_scripts' ) );
        add_action( 'admin_enqueue_scripts', array( __CLASS__, 'admin_scripts' ) );
    }

    /**
     * Get styles for the frontend.
     *
     * @return array
     */
    private static function get_styles() {
        return apply_filters(
            'bdpay_enqueue_styles',
            array(
                'bdpay-frontend' => array(
                    'src'     => BDPAY_ASSETS_URL . 'css/frontend.css',
                    'deps'    => array(),
                    'version' => BDPAY_VERSION,
                    'media'   => 'all',
                    'has_rtl' => false,
                ),
            )
        );
    }

    /**
     * Return asset URL.
     *
     * @param string $path Assets path.
     * @return string
     */
    private static function get_asset_url( $path ) {
        return apply_filters( 'bdpay_get_asset_url', plugins_url( $path, BDPAY_PLUGIN_FILE ), $path );
    }

    /**
     * Register a script for use.
     *
     * @param string   $handle    Name of the script. Should be unique.
     * @param string   $path      Full URL of the script, or path of the script relative to the WordPress root directory.
     * @param string[] $deps      An array of registered script handles this script depends on.
     * @param string   $version   String specifying script version number.
     * @param boolean  $in_footer Whether to enqueue the script before </body> instead of in the <head>.
     */
    private static function register_script( $handle, $path, $deps = array( 'jquery' ), $version = BDPAY_VERSION, $in_footer = true ) {
        wp_register_script( $handle, $path, $deps, $version, $in_footer );
    }

    /**
     * Register and enqueue a script for use.
     *
     * @param string   $handle    Name of the script. Should be unique.
     * @param string   $path      Full URL of the script, or path of the script relative to the WordPress root directory.
     * @param string[] $deps      An array of registered script handles this script depends on.
     * @param string   $version   String specifying script version number.
     * @param boolean  $in_footer Whether to enqueue the script before </body> instead of in the <head>.
     */
    private static function enqueue_script( $handle, $path, $deps = array( 'jquery' ), $version = BDPAY_VERSION, $in_footer = true ) {
        wp_enqueue_script( $handle, $path, $deps, $version, $in_footer );
    }

    /**
     * Register a style for use.
     *
     * @param string   $handle Name of the stylesheet. Should be unique.
     * @param string   $path   Full URL of the stylesheet, or path of the stylesheet relative to the WordPress root directory.
     * @param string[] $deps   An array of registered stylesheet handles this stylesheet depends on.
     * @param string   $version String specifying stylesheet version number.
     * @param string   $media  The media for which this stylesheet has been defined.
     * @param boolean  $has_rtl If has RTL version to load too.
     */
    private static function register_style( $handle, $path, $deps = array(), $version = BDPAY_VERSION, $media = 'all', $has_rtl = false ) {
        wp_register_style( $handle, $path, $deps, $version, $media );

        if ( $has_rtl ) {
            wp_style_add_data( $handle, 'rtl', 'replace' );
        }
    }

    /**
     * Register and enqueue a styles for use.
     *
     * @param string   $handle Name of the stylesheet. Should be unique.
     * @param string   $path   Full URL of the stylesheet, or path of the stylesheet relative to the WordPress root directory.
     * @param string[] $deps   An array of registered stylesheet handles this stylesheet depends on.
     * @param string   $version String specifying stylesheet version number.
     * @param string   $media  The media for which this stylesheet has been defined.
     * @param boolean  $has_rtl If has RTL version to load too.
     */
    private static function enqueue_style( $handle, $path, $deps = array(), $version = BDPAY_VERSION, $media = 'all', $has_rtl = false ) {
        wp_enqueue_style( $handle, $path, $deps, $version, $media );

        if ( $has_rtl ) {
            wp_style_add_data( $handle, 'rtl', 'replace' );
        }
    }

    /**
     * Register/queue frontend scripts.
     */
    public static function load_scripts() {
        global $post;

        if ( ! did_action( 'before_bdpay_mmpg_init' ) ) {
            return;
        }

        // CSS Styles.
        $enqueue_styles = self::get_styles();
        if ( $enqueue_styles ) {
            foreach ( $enqueue_styles as $handle => $args ) {
                if ( ! isset( $args['has_rtl'] ) ) {
                    $args['has_rtl'] = false;
                }

                self::enqueue_style( $handle, $args['src'], $args['deps'], $args['version'], $args['media'], $args['has_rtl'] );
            }
        }

        // Load scripts only on checkout page.
        if ( is_checkout() || ( is_object( $post ) && has_shortcode( $post->post_content, 'bdpay_payment_form' ) ) ) {
            self::enqueue_script(
                'bdpay-checkout',
                self::get_asset_url( 'assets/js/frontend/checkout.js' ),
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );

            // Localize script for AJAX.
            wp_localize_script(
                'bdpay-checkout',
                'bdpay_params',
                array(
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'nonce'    => wp_create_nonce( 'bdpay_checkout' ),
                    'i18n'     => array(
                        'copied'           => esc_html__( 'Copied!', 'bdpay' ),
                        'copy_failed'      => esc_html__( 'Copy failed', 'bdpay' ),
                        'select_method'    => esc_html__( 'Please select a payment method.', 'bdpay' ),
                        'enter_transaction' => esc_html__( 'Please enter the transaction ID.', 'bdpay' ),
                        'enter_phone'      => esc_html__( 'Please enter your phone number.', 'bdpay' ),
                        'invalid_phone'    => esc_html__( 'Please enter a valid Bangladesh mobile number.', 'bdpay' ),
                    ),
                )
            );
        }
    }

    /**
     * Register/queue admin scripts.
     *
     * @param string $hook Page hook.
     */
    public static function admin_scripts( $hook ) {
        $screen = get_current_screen();

        // Load admin CSS on BDPay pages.
        if ( in_array( $hook, array( 'bdpay_page_bdpay-settings', 'bdpay_page_bdpay-transactions' ), true ) || 
             ( $screen && in_array( $screen->id, array( 'shop_order', 'woocommerce_page_wc-orders' ), true ) ) ) {
            
            self::enqueue_style(
                'bdpay-admin',
                self::get_asset_url( 'assets/css/admin.css' ),
                array(),
                BDPAY_VERSION
            );
        }

        // Load admin JS on specific pages.
        if ( 'bdpay_page_bdpay-transactions' === $hook ) {
            self::enqueue_script(
                'bdpay-admin-transactions',
                self::get_asset_url( 'assets/js/admin/transactions.js' ),
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );

            wp_localize_script(
                'bdpay-admin-transactions',
                'bdpay_admin_params',
                array(
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'nonce'    => wp_create_nonce( 'bdpay_verify_payment' ),
                    'i18n'     => array(
                        'processing'    => esc_html__( 'Processing...', 'bdpay' ),
                        'verify_confirm' => esc_html__( 'Are you sure you want to verify this payment?', 'bdpay' ),
                        'reject_confirm' => esc_html__( 'Are you sure you want to reject this payment?', 'bdpay' ),
                        'error_occurred' => esc_html__( 'An error occurred. Please try again.', 'bdpay' ),
                        'copied'        => esc_html__( 'Copied to clipboard!', 'bdpay' ),
                    ),
                )
            );
        }

        if ( 'bdpay_page_bdpay-settings' === $hook ) {
            self::enqueue_script(
                'bdpay-admin-settings',
                self::get_asset_url( 'assets/js/admin/settings.js' ),
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );
        }

        // Load order meta box scripts.
        if ( $screen && in_array( $screen->id, array( 'shop_order', 'woocommerce_page_wc-orders' ), true ) ) {
            self::enqueue_script(
                'bdpay-admin-order',
                self::get_asset_url( 'assets/js/admin/order.js' ),
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );

            wp_localize_script(
                'bdpay-admin-order',
                'bdpay_order_params',
                array(
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'nonce'    => wp_create_nonce( 'bdpay_verify_payment' ),
                    'i18n'     => array(
                        'processing'    => esc_html__( 'Processing...', 'bdpay' ),
                        'verify_confirm' => esc_html__( 'Are you sure you want to verify this payment?', 'bdpay' ),
                        'reject_confirm' => esc_html__( 'Are you sure you want to reject this payment?', 'bdpay' ),
                        'error_occurred' => esc_html__( 'An error occurred. Please try again.', 'bdpay' ),
                        'copied'        => esc_html__( 'Copied to clipboard!', 'bdpay' ),
                    ),
                )
            );
        }
    }
}

BDPay_Assets::init();
