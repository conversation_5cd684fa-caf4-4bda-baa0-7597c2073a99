<?php
/**
 * BDPay Admin
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Admin Class.
 */
class BDPay_Admin {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'admin_menu', array( $this, 'admin_menu' ) );
        add_action( 'admin_init', array( $this, 'admin_init' ) );
        add_filter( 'plugin_action_links_' . BDPAY_PLUGIN_BASENAME, array( $this, 'plugin_action_links' ) );
        add_filter( 'plugin_row_meta', array( $this, 'plugin_row_meta' ), 10, 2 );
    }

    /**
     * Add admin menu.
     */
    public function admin_menu() {
        add_submenu_page(
            'woocommerce',
            __( 'BDPay Settings', 'bdpay' ),
            __( 'BDPay', 'bdpay' ),
            'manage_woocommerce',
            'bdpay-settings',
            array( $this, 'settings_page' )
        );

        add_submenu_page(
            'woocommerce',
            __( 'BDPay Transactions', 'bdpay' ),
            __( 'BDPay Transactions', 'bdpay' ),
            'manage_woocommerce',
            'bdpay-transactions',
            array( $this, 'transactions_page' )
        );
    }

    /**
     * Admin init.
     */
    public function admin_init() {
        // Register settings
        $this->register_settings();
    }

    /**
     * Register settings.
     */
    private function register_settings() {
        $payment_methods = BDPay_Core::get_payment_methods();

        // General settings
        register_setting( 'bdpay_settings', 'bdpay_enable_screenshot', array(
            'sanitize_callback' => array( $this, 'sanitize_checkbox' ),
        ) );

        // Payment method settings
        foreach ( $payment_methods as $method => $label ) {
            register_setting( 'bdpay_settings', 'bdpay_enable_' . $method, array(
                'sanitize_callback' => array( $this, 'sanitize_checkbox' ),
            ) );
            register_setting( 'bdpay_settings', 'bdpay_wallet_' . $method, array(
                'sanitize_callback' => array( $this, 'sanitize_wallet_number' ),
            ) );
            register_setting( 'bdpay_settings', 'bdpay_instructions_' . $method, array(
                'sanitize_callback' => array( $this, 'sanitize_textarea' ),
            ) );
        }
    }

    /**
     * Settings page.
     */
    public function settings_page() {
        if ( isset( $_POST['submit'] ) ) {
            $this->save_settings();
        }

        $payment_methods = BDPay_Core::get_payment_methods();
        include BDPAY_TEMPLATES_PATH . 'admin/settings-page.php';
    }

    /**
     * Transactions page.
     */
    public function transactions_page() {
        $transactions = BDPay_Transaction::get_transactions( array(
            'limit' => 50
        ) );

        include BDPAY_TEMPLATES_PATH . 'admin/transactions-page.php';
    }

    /**
     * Save settings.
     */
    private function save_settings() {
        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            return;
        }

        check_admin_referer( 'bdpay-settings' );

        $payment_methods = BDPay_Core::get_payment_methods();

        // Save general settings
        update_option( 'bdpay_enable_screenshot', isset( $_POST['bdpay_enable_screenshot'] ) ? 'yes' : 'no' );
        update_option( 'bdpay_enable_notifications', isset( $_POST['bdpay_enable_notifications'] ) ? 'yes' : 'no' );

        // Save payment method settings
        foreach ( $payment_methods as $method => $label ) {
            update_option( 'bdpay_enable_' . $method, isset( $_POST['bdpay_enable_' . $method] ) ? 'yes' : 'no' );
            update_option( 'bdpay_wallet_' . $method, sanitize_text_field( $_POST['bdpay_wallet_' . $method] ?? '' ) );
            update_option( 'bdpay_instructions_' . $method, sanitize_textarea_field( $_POST['bdpay_instructions_' . $method] ?? '' ) );
        }

        add_action( 'admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __( 'Settings saved successfully.', 'bdpay' ) . '</p></div>';
        } );
    }

    /**
     * Add plugin action links.
     *
     * @param array $links
     * @return array
     */
    public function plugin_action_links( $links ) {
        $action_links = array(
            'settings' => '<a href="' . admin_url( 'admin.php?page=bdpay-settings' ) . '">' . __( 'Settings', 'bdpay' ) . '</a>',
        );

        return array_merge( $action_links, $links );
    }

    /**
     * Add plugin row meta.
     *
     * @param array  $links
     * @param string $file
     * @return array
     */
    public function plugin_row_meta( $links, $file ) {
        if ( BDPAY_PLUGIN_BASENAME === $file ) {
            $row_meta = array(
                'docs'    => '<a href="https://bdpay.dev/docs/" target="_blank">' . __( 'Documentation', 'bdpay' ) . '</a>',
                'support' => '<a href="https://wordpress.org/support/plugin/bdpay/" target="_blank">' . __( 'Support', 'bdpay' ) . '</a>',
            );

            return array_merge( $links, $row_meta );
        }

        return $links;
    }

    /**
     * Sanitize checkbox value.
     *
     * @param mixed $value
     * @return string
     */
    public function sanitize_checkbox( $value ) {
        return ( 'yes' === $value ) ? 'yes' : 'no';
    }

    /**
     * Sanitize wallet number.
     *
     * @param string $value
     * @return string
     */
    public function sanitize_wallet_number( $value ) {
        // Remove all non-numeric characters except +
        $sanitized = preg_replace( '/[^0-9+]/', '', $value );

        // Validate Bangladesh mobile number format
        if ( preg_match( '/^(\+88)?01[3-9]\d{8}$/', $sanitized ) ) {
            return $sanitized;
        }

        return sanitize_text_field( $value );
    }

    /**
     * Sanitize textarea content.
     *
     * @param string $value
     * @return string
     */
    public function sanitize_textarea( $value ) {
        return sanitize_textarea_field( $value );
    }
}

new BDPay_Admin();
