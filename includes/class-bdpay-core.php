<?php
/**
 * BDPay Core Class
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Core Class.
 */
class BDPay_Core {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'plugins_loaded', array( $this, 'init' ), 11 );
        add_action( 'woocommerce_loaded', array( $this, 'woocommerce_loaded' ) );
        add_action( 'init', array( $this, 'late_init' ), 20 );
    }

    /**
     * Initialize the plugin.
     */
    public function init() {
        // Only initialize if WooCommerce is active
        if ( ! class_exists( 'WooCommerce' ) ) {
            return;
        }

        // Load the gateway class
        $this->load_gateway_class();

        // Add WooCommerce payment gateway
        add_filter( 'woocommerce_payment_gateways', array( $this, 'add_gateway_class' ) );
        
        // Add custom order statuses
        add_action( 'init', array( $this, 'register_order_statuses' ) );
        add_filter( 'wc_order_statuses', array( $this, 'add_order_statuses' ) );
        
        // Add order meta box
        add_action( 'add_meta_boxes', array( $this, 'add_order_meta_box' ) );
        
        // Handle order actions
        add_action( 'wp_ajax_bdpay_verify_payment', array( $this, 'verify_payment_ajax' ) );
        
        // Enqueue scripts
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_frontend_scripts' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

        // Add admin notices for debugging
        add_action( 'admin_notices', array( $this, 'admin_notices' ) );
    }

    /**
     * Late initialization as fallback.
     */
    public function late_init() {
        if ( class_exists( 'WooCommerce' ) && ! has_filter( 'woocommerce_payment_gateways', array( $this, 'add_gateway_class' ) ) ) {
            $this->load_gateway_class();
            add_filter( 'woocommerce_payment_gateways', array( $this, 'add_gateway_class' ) );
        }
    }

    /**
     * Initialize when WooCommerce is loaded.
     */
    public function woocommerce_loaded() {
        // Load the gateway class if not already loaded
        $this->load_gateway_class();

        // Add WooCommerce payment gateway
        add_filter( 'woocommerce_payment_gateways', array( $this, 'add_gateway_class' ) );
    }

    /**
     * Load the gateway class.
     */
    public function load_gateway_class() {
        if ( ! class_exists( 'BDPay_Gateway' ) && class_exists( 'WC_Payment_Gateway' ) ) {
            include_once BDPAY_INCLUDES_PATH . 'gateways/class-bdpay-gateway.php';
        }
    }

    /**
     * Add the gateway to WooCommerce.
     *
     * @param array $gateways
     * @return array
     */
    public function add_gateway_class( $gateways ) {
        // Make sure the class is loaded
        $this->load_gateway_class();

        if ( class_exists( 'BDPay_Gateway' ) ) {
            $gateways[] = 'BDPay_Gateway';

            // Debug log
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( 'BDPay: Gateway class added to WooCommerce payment gateways' );
            }
        } else {
            // Debug log
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( 'BDPay: Gateway class not found when trying to register' );
            }
        }
        return $gateways;
    }

    /**
     * Register custom order statuses.
     */
    public function register_order_statuses() {
        register_post_status( 'wc-bdpay-pending', array(
            'label'                     => _x( 'Pending BDPay Payment', 'Order status', 'bdpay' ),
            'public'                    => true,
            'exclude_from_search'       => false,
            'show_in_admin_all_list'    => true,
            'show_in_admin_status_list' => true,
            'label_count'               => _n_noop( 'Pending BDPay Payment <span class="count">(%s)</span>', 'Pending BDPay Payment <span class="count">(%s)</span>', 'bdpay' ),
        ) );

        register_post_status( 'wc-bdpay-verifying', array(
            'label'                     => _x( 'Verifying BDPay Payment', 'Order status', 'bdpay' ),
            'public'                    => true,
            'exclude_from_search'       => false,
            'show_in_admin_all_list'    => true,
            'show_in_admin_status_list' => true,
            'label_count'               => _n_noop( 'Verifying BDPay Payment <span class="count">(%s)</span>', 'Verifying BDPay Payment <span class="count">(%s)</span>', 'bdpay' ),
        ) );
    }

    /**
     * Add custom order statuses to list.
     *
     * @param array $order_statuses
     * @return array
     */
    public function add_order_statuses( $order_statuses ) {
        $new_order_statuses = array();

        // Add new order status after pending
        foreach ( $order_statuses as $key => $status ) {
            $new_order_statuses[ $key ] = $status;

            if ( 'wc-pending' === $key ) {
                $new_order_statuses['wc-bdpay-pending'] = _x( 'Pending BDPay Payment', 'Order status', 'bdpay' );
                $new_order_statuses['wc-bdpay-verifying'] = _x( 'Verifying BDPay Payment', 'Order status', 'bdpay' );
            }
        }

        return $new_order_statuses;
    }

    /**
     * Add meta box to order edit page.
     */
    public function add_order_meta_box() {
        add_meta_box(
            'bdpay-payment-details',
            __( 'BDPay Payment Details', 'bdpay' ),
            array( $this, 'order_meta_box_content' ),
            'shop_order',
            'normal',
            'high'
        );
    }

    /**
     * Meta box content for order edit page.
     *
     * @param WP_Post $post
     */
    public function order_meta_box_content( $post ) {
        $order = wc_get_order( $post->ID );
        
        if ( ! $order || 'bdpay' !== $order->get_payment_method() ) {
            return;
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order->get_id() );
        
        if ( ! $transaction ) {
            echo '<p>' . __( 'No BDPay transaction found for this order.', 'bdpay' ) . '</p>';
            return;
        }

        include BDPAY_TEMPLATES_PATH . 'admin/order-meta-box.php';
    }

    /**
     * Handle payment verification AJAX.
     */
    public function verify_payment_ajax() {
        check_ajax_referer( 'bdpay_verify_payment', 'nonce' );

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( __( 'You do not have permission to perform this action.', 'bdpay' ) );
        }

        $order_id = intval( $_POST['order_id'] );
        $action = sanitize_text_field( $_POST['action_type'] );

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( __( 'Order not found.', 'bdpay' ) );
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order_id );
        if ( ! $transaction ) {
            wp_send_json_error( __( 'Transaction not found.', 'bdpay' ) );
        }

        switch ( $action ) {
            case 'verify':
                $transaction->update_status( 'verified' );
                $order->update_status( 'processing', __( 'Payment verified via BDPay.', 'bdpay' ) );
                $order->payment_complete( $transaction->get_transaction_id() );
                
                // Send email notification
                do_action( 'bdpay_payment_verified', $order, $transaction );
                
                wp_send_json_success( __( 'Payment verified successfully.', 'bdpay' ) );
                break;

            case 'reject':
                $transaction->update_status( 'rejected' );
                $order->update_status( 'failed', __( 'Payment rejected via BDPay.', 'bdpay' ) );
                
                // Send email notification
                do_action( 'bdpay_payment_rejected', $order, $transaction );
                
                wp_send_json_success( __( 'Payment rejected.', 'bdpay' ) );
                break;

            default:
                wp_send_json_error( __( 'Invalid action.', 'bdpay' ) );
        }
    }

    /**
     * Enqueue frontend scripts.
     */
    public function enqueue_frontend_scripts() {
        if ( is_checkout() || is_account_page() ) {
            wp_enqueue_script(
                'bdpay-frontend',
                BDPAY_ASSETS_URL . 'js/frontend.js',
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );

            wp_enqueue_style(
                'bdpay-frontend',
                BDPAY_ASSETS_URL . 'css/frontend.css',
                array(),
                BDPAY_VERSION
            );

            wp_localize_script( 'bdpay-frontend', 'bdpay_params', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'bdpay_frontend' ),
                'i18n'     => array(
                    'transaction_id_required' => __( 'Transaction ID is required.', 'bdpay' ),
                    'phone_required'          => __( 'Sender phone number is required.', 'bdpay' ),
                    'invalid_phone'           => __( 'Please enter a valid phone number.', 'bdpay' ),
                ),
            ) );
        }
    }

    /**
     * Enqueue admin scripts.
     */
    public function enqueue_admin_scripts( $hook ) {
        if ( 'post.php' === $hook || 'post-new.php' === $hook ) {
            global $post;
            if ( $post && 'shop_order' === $post->post_type ) {
                wp_enqueue_script(
                    'bdpay-admin',
                    BDPAY_ASSETS_URL . 'js/admin.js',
                    array( 'jquery' ),
                    BDPAY_VERSION,
                    true
                );

                wp_enqueue_style(
                    'bdpay-admin',
                    BDPAY_ASSETS_URL . 'css/admin.css',
                    array(),
                    BDPAY_VERSION
                );

                wp_localize_script( 'bdpay-admin', 'bdpay_admin_params', array(
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'nonce'    => wp_create_nonce( 'bdpay_verify_payment' ),
                    'i18n'     => array(
                        'confirm_verify' => __( 'Are you sure you want to verify this payment?', 'bdpay' ),
                        'confirm_reject' => __( 'Are you sure you want to reject this payment?', 'bdpay' ),
                    ),
                ) );
            }
        }
    }

    /**
     * Get available payment methods.
     *
     * @return array
     */
    public static function get_payment_methods() {
        return apply_filters( 'bdpay_payment_methods', array(
            'bkash'     => __( 'bKash', 'bdpay' ),
            'nagad'     => __( 'Nagad', 'bdpay' ),
            'rocket'    => __( 'Rocket', 'bdpay' ),
            'upay'      => __( 'Upay', 'bdpay' ),
            'surecash'  => __( 'SureCash', 'bdpay' ),
        ) );
    }

    /**
     * Get enabled payment methods.
     *
     * @return array
     */
    public static function get_enabled_payment_methods() {
        $methods = self::get_payment_methods();
        $enabled = array();

        foreach ( $methods as $key => $label ) {
            if ( 'yes' === get_option( 'bdpay_enable_' . $key, 'no' ) ) {
                $enabled[ $key ] = $label;
            }
        }

        return $enabled;
    }

    /**
     * Get wallet number for payment method.
     *
     * @param string $method
     * @return string
     */
    public static function get_wallet_number( $method ) {
        return get_option( 'bdpay_wallet_' . $method, '' );
    }

    /**
     * Get instructions for payment method.
     *
     * @param string $method
     * @return string
     */
    public static function get_instructions( $method ) {
        return get_option( 'bdpay_instructions_' . $method, '' );
    }

    /**
     * Admin notices for debugging.
     */
    public function admin_notices() {
        // Only show on WooCommerce settings page
        if ( ! isset( $_GET['page'] ) || $_GET['page'] !== 'wc-settings' ) {
            return;
        }

        // Only show if WP_DEBUG is enabled
        if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG ) {
            return;
        }

        $gateways = WC()->payment_gateways()->payment_gateways();
        $bdpay_registered = isset( $gateways['bdpay'] );

        if ( ! $bdpay_registered ) {
            echo '<div class="notice notice-warning"><p>';
            echo '<strong>BDPay Debug:</strong> Gateway not found in WooCommerce payment gateways. ';
            echo 'Class exists: ' . ( class_exists( 'BDPay_Gateway' ) ? 'Yes' : 'No' ) . '. ';
            echo 'WooCommerce active: ' . ( class_exists( 'WooCommerce' ) ? 'Yes' : 'No' ) . '.';
            echo '</p></div>';
        } else {
            echo '<div class="notice notice-success"><p>';
            echo '<strong>BDPay Debug:</strong> Gateway successfully registered with WooCommerce.';
            echo '</p></div>';
        }
    }
}

new BDPay_Core();
