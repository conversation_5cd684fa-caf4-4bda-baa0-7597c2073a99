<?php
/**
 * BDPay AJAX Handler
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Ajax Class.
 */
class BDPay_Ajax {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'wp_ajax_bdpay_submit_payment', array( $this, 'submit_payment' ) );
        add_action( 'wp_ajax_nopriv_bdpay_submit_payment', array( $this, 'submit_payment' ) );
        add_action( 'wp_ajax_bdpay_verify_payment', array( $this, 'verify_payment' ) );
        add_action( 'wp_ajax_bdpay_get_wallet_info', array( $this, 'get_wallet_info' ) );
        add_action( 'wp_ajax_nopriv_bdpay_get_wallet_info', array( $this, 'get_wallet_info' ) );
    }

    /**
     * Handle payment submission.
     */
    public function submit_payment() {
        check_ajax_referer( 'bdpay_frontend', 'nonce' );

        $order_id = intval( $_POST['order_id'] );
        $payment_method = sanitize_text_field( $_POST['payment_method'] );
        $transaction_id = sanitize_text_field( $_POST['transaction_id'] );
        $sender_phone = sanitize_text_field( $_POST['sender_phone'] );
        $notes = sanitize_textarea_field( $_POST['notes'] ?? '' );

        // Validate inputs
        if ( empty( $order_id ) || empty( $payment_method ) || empty( $transaction_id ) || empty( $sender_phone ) ) {
            wp_send_json_error( __( 'All required fields must be filled.', 'bdpay' ) );
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( __( 'Invalid order.', 'bdpay' ) );
        }

        // Check if payment already submitted
        $existing_transaction = BDPay_Transaction::get_by_order_id( $order_id );
        if ( $existing_transaction && 'pending' !== $existing_transaction->get_status() ) {
            wp_send_json_error( __( 'Payment has already been submitted for this order.', 'bdpay' ) );
        }

        // Validate phone number
        if ( ! $this->validate_phone_number( $sender_phone ) ) {
            wp_send_json_error( __( 'Please enter a valid Bangladesh phone number.', 'bdpay' ) );
        }

        // Check for duplicate transaction ID
        $duplicate_transaction = BDPay_Transaction::get_by_transaction_id( $transaction_id );
        if ( $duplicate_transaction && $duplicate_transaction->get_order_id() !== $order_id ) {
            wp_send_json_error( __( 'This transaction ID has already been used. Please check your transaction ID.', 'bdpay' ) );
        }

        // Handle screenshot upload
        $screenshot_url = '';
        if ( ! empty( $_FILES['screenshot'] ) && 'yes' === get_option( 'bdpay_enable_screenshot', 'no' ) ) {
            $screenshot_url = $this->handle_screenshot_upload( $_FILES['screenshot'] );
            if ( is_wp_error( $screenshot_url ) ) {
                wp_send_json_error( $screenshot_url->get_error_message() );
            }
        }

        // Create or update transaction
        $transaction_data = array(
            'order_id'       => $order_id,
            'transaction_id' => $transaction_id,
            'sender_phone'   => $sender_phone,
            'payment_method' => $payment_method,
            'amount'         => $order->get_total(),
            'status'         => 'submitted',
            'screenshot_url' => $screenshot_url,
            'notes'          => $notes,
        );

        if ( $existing_transaction ) {
            $transaction = $existing_transaction;
            $transaction->update( $transaction_data );
        } else {
            $transaction = new BDPay_Transaction();
            $transaction->create( $transaction_data );
        }

        // Update order status
        $order->update_status( 'bdpay-verifying', __( 'BDPay payment submitted, awaiting verification.', 'bdpay' ) );

        // Add order note
        $order->add_order_note( sprintf(
            __( 'BDPay payment submitted. Method: %1$s, Transaction ID: %2$s, Phone: %3$s', 'bdpay' ),
            $payment_method,
            $transaction_id,
            $sender_phone
        ) );

        // Send email notifications
        do_action( 'bdpay_payment_submitted', $order, $transaction );

        wp_send_json_success( array(
            'message' => __( 'Payment details submitted successfully. We will verify your payment and update your order status.', 'bdpay' ),
            'redirect' => $order->get_view_order_url()
        ) );
    }

    /**
     * Handle payment verification.
     */
    public function verify_payment() {
        check_ajax_referer( 'bdpay_verify_payment', 'nonce' );

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_send_json_error( __( 'You do not have permission to perform this action.', 'bdpay' ) );
        }

        $order_id = intval( $_POST['order_id'] );
        $action = sanitize_text_field( $_POST['action_type'] );

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( __( 'Order not found.', 'bdpay' ) );
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order_id );
        if ( ! $transaction ) {
            wp_send_json_error( __( 'Transaction not found.', 'bdpay' ) );
        }

        switch ( $action ) {
            case 'verify':
                $transaction->update_status( 'verified' );
                $order->update_status( 'processing', __( 'Payment verified via BDPay.', 'bdpay' ) );
                $order->payment_complete( $transaction->get_transaction_id() );
                
                // Send email notification
                do_action( 'bdpay_payment_verified', $order, $transaction );
                
                wp_send_json_success( __( 'Payment verified successfully.', 'bdpay' ) );
                break;

            case 'reject':
                $transaction->update_status( 'rejected' );
                $order->update_status( 'failed', __( 'Payment rejected via BDPay.', 'bdpay' ) );
                
                // Send email notification
                do_action( 'bdpay_payment_rejected', $order, $transaction );
                
                wp_send_json_success( __( 'Payment rejected.', 'bdpay' ) );
                break;

            default:
                wp_send_json_error( __( 'Invalid action.', 'bdpay' ) );
        }
    }

    /**
     * Get wallet information for a payment method.
     */
    public function get_wallet_info() {
        check_ajax_referer( 'bdpay_frontend', 'nonce' );

        $method = sanitize_text_field( $_POST['method'] ?? '' );
        
        if ( empty( $method ) ) {
            wp_send_json_error( __( 'Payment method is required.', 'bdpay' ) );
        }

        $enabled_methods = BDPay_Core::get_enabled_payment_methods();
        
        if ( ! array_key_exists( $method, $enabled_methods ) ) {
            wp_send_json_error( __( 'Invalid payment method.', 'bdpay' ) );
        }

        $wallet_number = BDPay_Core::get_wallet_number( $method );
        $instructions = BDPay_Core::get_instructions( $method );

        wp_send_json_success( array(
            'wallet_number' => $wallet_number,
            'instructions' => $instructions,
            'method_label' => $enabled_methods[ $method ]
        ) );
    }

    /**
     * Validate Bangladesh phone number.
     *
     * @param string $phone
     * @return bool
     */
    private function validate_phone_number( $phone ) {
        // Remove any non-digit characters
        $phone = preg_replace( '/[^0-9]/', '', $phone );
        
        // Check if it's a valid Bangladesh mobile number
        // Bangladesh mobile numbers: 01XXXXXXXXX (11 digits) or 8801XXXXXXXXX (13 digits)
        if ( preg_match( '/^(01[3-9]\d{8}|8801[3-9]\d{8})$/', $phone ) ) {
            return true;
        }
        
        return false;
    }

    /**
     * Handle screenshot upload.
     *
     * @param array $file
     * @return string|WP_Error
     */
    private function handle_screenshot_upload( $file ) {
        if ( ! function_exists( 'wp_handle_upload' ) ) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        // Check file size (max 5MB)
        if ( $file['size'] > 5 * 1024 * 1024 ) {
            return new WP_Error( 'file_too_large', __( 'File size must be less than 5MB.', 'bdpay' ) );
        }

        $upload_overrides = array(
            'test_form' => false,
            'mimes'     => array(
                'jpg|jpeg|jpe' => 'image/jpeg',
                'png'          => 'image/png',
                'gif'          => 'image/gif',
                'webp'         => 'image/webp',
            ),
        );

        $uploaded_file = wp_handle_upload( $file, $upload_overrides );

        if ( isset( $uploaded_file['error'] ) ) {
            return new WP_Error( 'upload_error', $uploaded_file['error'] );
        }

        return $uploaded_file['url'];
    }
}

new BDPay_Ajax();
