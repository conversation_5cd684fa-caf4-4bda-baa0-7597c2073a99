<?php
/**
 * BDPay Frontend
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Frontend Class.
 */
class BDPay_Frontend {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
        add_action( 'woocommerce_thankyou', array( $this, 'thankyou_page_content' ), 20 );
        add_filter( 'woocommerce_my_account_my_orders_actions', array( $this, 'add_order_actions' ), 10, 2 );
        add_action( 'woocommerce_view_order', array( $this, 'view_order_payment_details' ), 20 );
    }

    /**
     * Enqueue frontend scripts and styles.
     */
    public function enqueue_scripts() {
        if ( is_checkout() || is_account_page() || is_wc_endpoint_url( 'view-order' ) ) {
            wp_enqueue_script(
                'bdpay-frontend',
                BDPAY_ASSETS_URL . 'js/frontend.js',
                array( 'jquery' ),
                BDPAY_VERSION,
                true
            );

            wp_enqueue_style(
                'bdpay-frontend',
                BDPAY_ASSETS_URL . 'css/frontend.css',
                array(),
                BDPAY_VERSION
            );

            wp_localize_script( 'bdpay-frontend', 'bdpay_params', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'bdpay_frontend' ),
                'i18n'     => array(
                    'transaction_id_required' => __( 'Transaction ID is required.', 'bdpay' ),
                    'phone_required'          => __( 'Sender phone number is required.', 'bdpay' ),
                    'invalid_phone'           => __( 'Please enter a valid phone number.', 'bdpay' ),
                    'payment_method_required' => __( 'Please select a payment method.', 'bdpay' ),
                    'submitting'              => __( 'Submitting...', 'bdpay' ),
                    'submit_payment'          => __( 'Submit Payment Details', 'bdpay' ),
                    'copied'                  => __( 'Copied to clipboard!', 'bdpay' ),
                    'copy_failed'             => __( 'Failed to copy. Please copy manually.', 'bdpay' ),
                ),
            ) );
        }
    }

    /**
     * Add content to thank you page.
     *
     * @param int $order_id
     */
    public function thankyou_page_content( $order_id ) {
        $order = wc_get_order( $order_id );
        
        if ( ! $order || 'bdpay' !== $order->get_payment_method() ) {
            return;
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order_id );
        
        if ( $transaction && 'pending' !== $transaction->get_status() ) {
            // Payment already submitted
            $this->display_payment_status( $order, $transaction );
        } else {
            // Show payment form
            $this->display_payment_form( $order );
        }
    }

    /**
     * Add custom actions to my account orders.
     *
     * @param array $actions
     * @param WC_Order $order
     * @return array
     */
    public function add_order_actions( $actions, $order ) {
        if ( 'bdpay' === $order->get_payment_method() && $order->has_status( array( 'bdpay-pending', 'bdpay-verifying' ) ) ) {
            $transaction = BDPay_Transaction::get_by_order_id( $order->get_id() );
            
            if ( ! $transaction || 'pending' === $transaction->get_status() ) {
                $actions['submit_payment'] = array(
                    'url'  => $order->get_view_order_url() . '#bdpay-payment-form',
                    'name' => __( 'Submit Payment', 'bdpay' ),
                );
            }
        }

        return $actions;
    }

    /**
     * Display payment details on view order page.
     *
     * @param int $order_id
     */
    public function view_order_payment_details( $order_id ) {
        $order = wc_get_order( $order_id );
        
        if ( ! $order || 'bdpay' !== $order->get_payment_method() ) {
            return;
        }

        $transaction = BDPay_Transaction::get_by_order_id( $order_id );
        
        // Check if user wants to resubmit payment
        if ( isset( $_GET['bdpay_resubmit'] ) && '1' === $_GET['bdpay_resubmit'] ) {
            if ( $transaction ) {
                $transaction->update_status( 'pending' );
            }
            $transaction = null; // Force showing the form
        }
        
        if ( $transaction && 'pending' !== $transaction->get_status() ) {
            // Payment already submitted
            $this->display_payment_status( $order, $transaction );
        } else {
            // Show payment form
            $this->display_payment_form( $order );
        }
    }

    /**
     * Display payment form.
     *
     * @param WC_Order $order
     */
    private function display_payment_form( $order ) {
        $enabled_methods = BDPay_Core::get_enabled_payment_methods();
        
        if ( empty( $enabled_methods ) ) {
            echo '<div class="woocommerce-error">' . __( 'No payment methods are currently available. Please contact the store administrator.', 'bdpay' ) . '</div>';
            return;
        }

        include BDPAY_TEMPLATES_PATH . 'checkout/payment-form.php';
    }

    /**
     * Display payment status.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     */
    private function display_payment_status( $order, $transaction ) {
        include BDPAY_TEMPLATES_PATH . 'checkout/payment-submitted.php';
    }

    /**
     * Get payment method icon URL.
     *
     * @param string $method
     * @return string
     */
    public static function get_method_icon_url( $method ) {
        $icon_file = BDPAY_PLUGIN_PATH . 'assets/images/' . $method . '.png';
        
        if ( file_exists( $icon_file ) ) {
            return BDPAY_ASSETS_URL . 'images/' . $method . '.png';
        }
        
        return BDPAY_ASSETS_URL . 'images/default-method.png';
    }

    /**
     * Format phone number for display.
     *
     * @param string $phone
     * @return string
     */
    public static function format_phone_number( $phone ) {
        // Remove any non-digit characters
        $phone = preg_replace( '/[^0-9]/', '', $phone );
        
        // Format Bangladesh mobile numbers
        if ( preg_match( '/^(01[3-9]\d{8})$/', $phone ) ) {
            return substr( $phone, 0, 3 ) . '-' . substr( $phone, 3, 4 ) . '-' . substr( $phone, 7 );
        } elseif ( preg_match( '/^(8801[3-9]\d{8})$/', $phone ) ) {
            return '+88-' . substr( $phone, 2, 3 ) . '-' . substr( $phone, 5, 4 ) . '-' . substr( $phone, 9 );
        }
        
        return $phone;
    }

    /**
     * Get formatted transaction status.
     *
     * @param string $status
     * @return string
     */
    public static function get_formatted_status( $status ) {
        switch ( $status ) {
            case 'pending':
                return __( 'Pending Submission', 'bdpay' );
            case 'submitted':
                return __( 'Submitted - Awaiting Verification', 'bdpay' );
            case 'verified':
                return __( 'Verified - Payment Confirmed', 'bdpay' );
            case 'rejected':
                return __( 'Rejected - Please Contact Support', 'bdpay' );
            default:
                return ucfirst( $status );
        }
    }

    /**
     * Get status CSS class.
     *
     * @param string $status
     * @return string
     */
    public static function get_status_class( $status ) {
        switch ( $status ) {
            case 'pending':
                return 'bdpay-status-pending';
            case 'submitted':
                return 'bdpay-status-submitted';
            case 'verified':
                return 'bdpay-status-verified';
            case 'rejected':
                return 'bdpay-status-rejected';
            default:
                return 'bdpay-status-' . $status;
        }
    }
}

new BDPay_Frontend();
