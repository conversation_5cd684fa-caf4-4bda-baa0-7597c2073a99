# BDPay - Manual Mobile Payment Gateway Documentation

## Table of Contents

1. [Overview](#overview)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Usage](#usage)
5. [Developer Guide](#developer-guide)
6. [Troubleshooting](#troubleshooting)
7. [FAQ](#faq)
8. [Support](#support)

## Overview

BDPay is a WordPress plugin that enables manual mobile payment processing for Bangladesh. It integrates with WooCommerce to provide a seamless checkout experience for customers using local Mobile Financial Services (MFS) like bKash, Nagad, Rocket, Upay, and SureCash.

### Key Features

- **Multiple MFS Support**: bKash, Nagad, Rocket, Upay, SureCash
- **WooCommerce Integration**: Seamless checkout experience
- **Manual Payment Processing**: Customer submits transaction details
- **Admin Verification**: Easy payment verification interface
- **Email Notifications**: Automatic notifications for all parties
- **Security Features**: Comprehensive data validation and sanitization
- **Translation Ready**: Full internationalization support

## Installation

### Automatic Installation

1. Log in to your WordPress admin panel
2. Navigate to **Plugins > Add New**
3. Search for "BDPay"
4. Click **Install Now** and then **Activate**

### Manual Installation

1. Download the plugin zip file
2. Upload to `/wp-content/plugins/` directory
3. Extract the zip file
4. Activate the plugin through the **Plugins** menu

### Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- WooCommerce 3.0 or higher
- MySQL 5.6 or higher

## Configuration

### Initial Setup

1. Go to **WooCommerce > BDPay** in your admin panel
2. Configure your MFS wallet numbers
3. Enable desired payment methods
4. Set up payment instructions
5. Configure email notifications
6. Save settings

### Payment Method Configuration

For each payment method (bKash, Nagad, etc.):

1. **Enable the method**: Check the enable checkbox
2. **Set wallet number**: Enter your MFS wallet number
3. **Add instructions**: Provide step-by-step payment instructions
4. **Save settings**

### WooCommerce Payment Gateway

1. Go to **WooCommerce > Settings > Payments**
2. Find **BDPay - Manual Mobile Payment**
3. Click **Manage** to configure gateway settings
4. Enable the gateway and configure title/description
5. Save changes

## Usage

### For Customers

1. **Checkout Process**:
   - Select "BDPay - Manual Mobile Payment" at checkout
   - Choose preferred MFS method
   - Complete order placement

2. **Payment Submission**:
   - Send money to displayed wallet number
   - Submit transaction ID and phone number
   - Optionally upload payment screenshot
   - Await verification

### For Merchants

1. **Order Management**:
   - View pending payments in WooCommerce orders
   - Check payment details in order meta box
   - Verify or reject payments
   - Process orders after verification

2. **Transaction Management**:
   - Access **WooCommerce > BDPay Transactions**
   - View all payment submissions
   - Filter by status or payment method
   - Export transaction data

## Developer Guide

### Hooks and Filters

#### Actions

```php
// Before BDPay initialization
do_action( 'before_bdpay_init' );

// After BDPay initialization
do_action( 'bdpay_init' );

// When BDPay is loaded
do_action( 'bdpay_loaded' );

// Payment submission
do_action( 'bdpay_payment_submitted', $order, $transaction );

// Payment verification
do_action( 'bdpay_payment_verified', $order, $transaction );

// Payment rejection
do_action( 'bdpay_payment_rejected', $order, $transaction );
```

#### Filters

```php
// Modify payment methods
add_filter( 'bdpay_payment_methods', 'custom_payment_methods' );

// Modify gateway icon
add_filter( 'bdpay_gateway_icon', 'custom_gateway_icon' );

// Modify template path
add_filter( 'bdpay_template_path', 'custom_template_path' );
```

### Custom Templates

Override templates by copying them to your theme:

```
your-theme/
├── bdpay/
│   ├── checkout/
│   │   ├── payment-fields.php
│   │   ├── payment-form.php
│   │   └── payment-submitted.php
│   └── admin/
│       └── order-meta-box.php
```

### Database Schema

#### bdpay_transactions Table

```sql
CREATE TABLE wp_bdpay_transactions (
  id bigint(20) unsigned NOT NULL auto_increment,
  order_id bigint(20) unsigned NOT NULL,
  transaction_id varchar(100) NOT NULL,
  sender_phone varchar(20) NOT NULL,
  payment_method varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  status varchar(20) NOT NULL DEFAULT 'pending',
  screenshot_url varchar(255) DEFAULT NULL,
  notes text DEFAULT NULL,
  date_created datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  date_modified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY order_id (order_id),
  KEY transaction_id (transaction_id),
  KEY status (status)
);
```

### API Reference

#### BDPay_Transaction Class

```php
// Create new transaction
$transaction = new BDPay_Transaction();
$transaction->create( $data );

// Get transaction by order ID
$transaction = BDPay_Transaction::get_by_order_id( $order_id );

// Update transaction status
$transaction->update_status( 'verified' );
```

#### BDPay_Core Class

```php
// Get enabled payment methods
$methods = BDPay_Core::get_enabled_payment_methods();

// Get wallet number
$wallet = BDPay_Core::get_wallet_number( 'bkash' );

// Get instructions
$instructions = BDPay_Core::get_instructions( 'nagad' );
```

## Troubleshooting

### Common Issues

#### Payment Gateway Not Showing

1. Ensure WooCommerce is active
2. Check if BDPay gateway is enabled
3. Verify at least one payment method is configured

#### Email Notifications Not Working

1. Check email notification settings
2. Verify WordPress mail configuration
3. Test with SMTP plugin if needed

#### Transaction Not Saving

1. Check database permissions
2. Verify table creation during activation
3. Check PHP error logs

#### File Upload Issues

1. Verify upload directory permissions
2. Check PHP upload limits
3. Ensure allowed file types

### Debug Mode

Enable debug mode by adding to wp-config.php:

```php
define( 'BDPAY_DEBUG', true );
define( 'BDPAY_TEMPLATE_DEBUG_MODE', true );
```

### Log Files

Check logs in **WooCommerce > Status > Logs**:
- `bdpay-{date}.log` - General logs
- `bdpay-security-{date}.log` - Security events

## FAQ

### General Questions

**Q: Is BDPay free?**
A: Yes, BDPay is completely free and open source.

**Q: Does it work without WooCommerce?**
A: No, BDPay requires WooCommerce to function.

**Q: Which payment methods are supported?**
A: bKash, Nagad, Rocket, Upay, and SureCash.

### Technical Questions

**Q: Can I customize the payment form?**
A: Yes, you can override templates in your theme.

**Q: How do I add custom payment methods?**
A: Use the `bdpay_payment_methods` filter.

**Q: Is the plugin translation ready?**
A: Yes, it includes .pot file and Bangla translation.

### Security Questions

**Q: Is customer data secure?**
A: Yes, all data is sanitized and validated.

**Q: Are there rate limits?**
A: Yes, 5 attempts per 15 minutes per IP.

**Q: How are files validated?**
A: Only image files under 5MB are allowed.

## Support

### Getting Help

1. **Documentation**: Check this documentation first
2. **WordPress Support**: https://wordpress.org/support/plugin/bdpay/
3. **GitHub Issues**: https://github.com/bdpay/bdpay-wordpress/issues

### Reporting Bugs

When reporting bugs, please include:
- WordPress version
- WooCommerce version
- PHP version
- Plugin version
- Error messages
- Steps to reproduce

### Feature Requests

Submit feature requests via:
- GitHub Issues
- WordPress Support Forum
- Email: <EMAIL>

### Contributing

We welcome contributions! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

### License

BDPay is licensed under GPL v2 or later.

---

For more information, visit: https://bdpay.dev/
