=== BDPay - Manual Mobile Payment Gateway ===
Contributors: mdnahidhasan
Tags: bkash, nagad, bangladesh, woocommerce, payment gateway
Requires at least: 5.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Manual mobile payment gateway for Bangladesh supporting bKash, Nagad, Rocket, Upay, and SureCash. Perfect for WooCommerce stores accepting manual MFS payments.

== Description ==

**BDPay** is a comprehensive WordPress plugin that enables Bangladeshi businesses to accept manual payments via local Mobile Financial Services (MFS) such as bKash, Nagad, Rocket, Upay, and SureCash.

Perfect for WooCommerce store owners, freelancers, and small businesses who need to accept manual mobile payments from customers in Bangladesh.

### 🚀 Key Features

* **Multiple MFS Support**: bKash, Nagad, Rocket, Upay, SureCash
* **Manual Payment Processing**: Customers send money manually and submit transaction details
* **WooCommerce Integration**: Seamless integration with WooCommerce checkout
* **Transaction Management**: Complete transaction logging and verification system
* **Admin Verification Panel**: Easy-to-use admin interface for payment verification
* **Email Notifications**: Automatic notifications for customers and admins
* **Screenshot Upload**: Optional payment screenshot upload for faster verification
* **Custom Instructions**: Add payment instructions for each MFS method
* **Order Status Management**: Custom order statuses for payment tracking
* **Security Features**: Comprehensive data sanitization and nonce verification
* **Translation Ready**: Full internationalization support with .pot file included

### 💼 Perfect For

* WooCommerce store owners in Bangladesh
* Local service providers & freelancers
* Small businesses without automated payment gateways
* NGOs and donation sites using local MFS
* Anyone accepting manual mobile payments in Bangladesh

### 🔧 How It Works

**For Customers:**
1. Select "BDPay - Manual Payment" at checkout
2. Choose preferred MFS (bKash, Nagad, etc.)
3. Send money to the displayed wallet number
4. Submit transaction ID and sender phone number
5. Optionally upload payment screenshot
6. Receive confirmation and await verification

**For Merchants:**
1. Configure wallet numbers for each MFS method
2. Receive email notifications for new payment submissions
3. Verify payments in WooCommerce admin
4. Mark orders as verified or rejected
5. Automatic order processing after verification

### 🛡️ Security & Compliance

* WordPress coding standards compliant
* Comprehensive data sanitization
* Nonce verification for all forms
* Capability checks for admin functions
* Secure file upload handling
* SQL injection prevention

### 🌐 Supported Payment Methods

* **bKash** - Bangladesh's leading mobile financial service
* **Nagad** - Digital financial service by Bangladesh Post Office
* **Rocket** - Mobile financial service by Dutch-Bangla Bank
* **Upay** - Mobile financial service by UCB Fintech
* **SureCash** - Mobile financial service by Sure Cash

### 📧 Email Notifications

* Payment submission confirmations
* Payment verification notifications
* Payment rejection alerts
* Customizable email templates
* Both admin and customer notifications

### 🎨 Admin Features

* Comprehensive settings panel
* Transaction management dashboard
* Order meta box with payment details
* Bulk verification actions
* Payment method configuration
* Custom instruction management

== Installation ==

### Automatic Installation

1. Log in to your WordPress admin panel
2. Go to Plugins > Add New
3. Search for "BDPay"
4. Click "Install Now" and then "Activate"

### Manual Installation

1. Download the plugin zip file
2. Upload to `/wp-content/plugins/` directory
3. Extract the zip file
4. Activate the plugin through the 'Plugins' menu in WordPress

### Configuration

1. Go to WooCommerce > BDPay in your admin panel
2. Configure your MFS wallet numbers
3. Enable desired payment methods
4. Set up payment instructions
5. Configure email notifications
6. Save settings

== Frequently Asked Questions ==

= Is this plugin free? =

Yes, BDPay is completely free and open source.

= Does this work with WooCommerce? =

Yes, BDPay is specifically designed for WooCommerce integration.

= Can I use this without WooCommerce? =

Currently, BDPay requires WooCommerce to function properly.

= Which mobile financial services are supported? =

BDPay supports bKash, Nagad, Rocket, Upay, and SureCash.

= Is this plugin secure? =

Yes, BDPay follows WordPress security best practices including data sanitization, nonce verification, and capability checks.

= Can customers upload payment screenshots? =

Yes, screenshot upload is an optional feature that can be enabled in settings.

= How do I verify payments? =

Payments can be verified through the WooCommerce order admin panel where you'll see payment details and verification buttons.

= Are email notifications automatic? =

Yes, email notifications are sent automatically for payment submissions, verifications, and rejections.

= Can I customize payment instructions? =

Yes, you can add custom instructions for each payment method in the settings.

= Is the plugin translation ready? =

Yes, BDPay includes a .pot file and is ready for translation into any language.

== Screenshots ==

1. Admin settings page for configuring payment methods
2. Checkout page with BDPay payment options
3. Payment submission form for customers
4. Order admin panel with payment verification
5. Transaction management dashboard
6. Email notification example

== Changelog ==

= 1.0.0 =
* Initial release
* WooCommerce payment gateway integration
* Support for bKash, Nagad, Rocket, Upay, SureCash
* Manual payment processing workflow
* Admin verification panel
* Email notification system
* Screenshot upload functionality
* Transaction logging and management
* Custom order statuses
* Security features and data sanitization
* Translation ready with .pot file
* Comprehensive documentation

== Upgrade Notice ==

= 1.0.0 =
Initial release of BDPay - Manual Mobile Payment Gateway for Bangladesh.

== Support ==

For support, feature requests, or bug reports, please visit:

* Plugin Support: https://wordpress.org/support/plugin/bdpay/
* Documentation: https://bdpay.dev/docs/
* GitHub Repository: https://github.com/bdpay/bdpay-wordpress

== Contributing ==

BDPay is open source and welcomes contributions. Visit our GitHub repository to contribute:
https://github.com/bdpay/bdpay-wordpress

== Privacy Policy ==

BDPay stores transaction information including:
* Order IDs and amounts
* Transaction IDs from mobile financial services
* Customer phone numbers
* Optional payment screenshots
* Payment verification status

This data is used solely for payment processing and verification. No data is shared with third parties except as required for payment processing.

== Credits ==

BDPay is developed and maintained by the BDPay Team with contributions from the WordPress community.

Special thanks to:
* WooCommerce team for the excellent e-commerce platform
* WordPress community for continuous support
* Bangladesh mobile financial service providers